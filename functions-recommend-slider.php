<?php
/**
 * Modern View All Button for Movie Sections
 * เพิ่มปุ่ม "ดูทั้งหมด" ที่ทันสมัยและสวยงาม
 */

// Modern View All Button CSS
function add_modern_view_all_styles() {
    ?>
    <style>
    /* Modern Section Header with View All Button */
    .movie-section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 30px 0 20px 0;
        padding: 0 20px;
    }

    .movie-section-title {
        font-size: 24px;
        font-weight: 700;
        color: #ffffff;
        margin: 0;
        letter-spacing: -0.5px;
    }

    .modern-view-all-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        position: relative;
        overflow: hidden;
    }

    .modern-view-all-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .modern-view-all-btn:hover::before {
        left: 100%;
    }

    .modern-view-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%);
    }

    .modern-view-all-btn:active {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    }

    .view-all-arrow {
        font-size: 16px;
        font-weight: bold;
        transition: transform 0.3s ease;
    }

    .modern-view-all-btn:hover .view-all-arrow {
        transform: translateX(3px);
    }

    /* Movie Grid Container */
    .movie-grid-container {
        padding: 0 20px;
        margin-bottom: 40px;
    }

    .movie-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
        max-width: 100%;
    }

    .movie-item {
        background: #1a1a1a;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        height: 320px;
    }

    .movie-item:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.5);
    }

    .movie-item a {
        display: block;
        text-decoration: none;
        color: inherit;
        height: 100%;
    }

    .movie-poster {
        position: relative;
        width: 100%;
        height: 240px;
        overflow: hidden;
    }

    .movie-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .movie-item:hover .movie-poster img {
        transform: scale(1.08);
    }

    .movie-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0.4), transparent, rgba(0,0,0,0.8));
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .movie-item:hover .movie-overlay {
        opacity: 1;
    }

    .movie-rating {
        display: flex;
        align-items: center;
        gap: 5px;
        background: rgba(255, 107, 53, 0.95);
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        align-self: flex-start;
        backdrop-filter: blur(10px);
    }

    .movie-rating .star-icon {
        color: #ffd700;
        font-size: 14px;
    }

    .movie-quality {
        background: rgba(0, 0, 0, 0.9);
        color: #00ff88;
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 11px;
        font-weight: bold;
        align-self: flex-end;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(0, 255, 136, 0.3);
    }

    .movie-info {
        padding: 16px;
        background: #1a1a1a;
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .movie-info h3 {
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        margin: 0 0 8px 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .movie-audio {
        font-size: 12px;
        color: #ff6b35;
        font-weight: 500;
        margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .movie-grid {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 16px;
        }
    }

    @media (max-width: 768px) {
        .movie-section-header {
            padding: 0 15px;
            margin: 20px 0 15px 0;
        }

        .movie-section-title {
            font-size: 20px;
        }

        .modern-view-all-btn {
            padding: 10px 16px;
            font-size: 13px;
        }

        .movie-grid-container {
            padding: 0 15px;
        }

        .movie-grid {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 12px;
        }

        .movie-item {
            height: 280px;
        }

        .movie-poster {
            height: 200px;
        }

        .movie-info {
            height: 80px;
            padding: 12px;
        }
    }

    @media (max-width: 480px) {
        .movie-section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .movie-section-title {
            font-size: 18px;
        }

        .modern-view-all-btn {
            align-self: flex-end;
            padding: 8px 14px;
            font-size: 12px;
        }

        .movie-grid {
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 10px;
        }

        .movie-item {
            height: 260px;
        }

        .movie-poster {
            height: 180px;
        }

        .movie-info {
            height: 80px;
            padding: 10px;
        }

        .movie-info h3 {
            font-size: 13px;
        }
    }
    </style>
    <?php
}
add_action('wp_head', 'add_modern_view_all_styles');

// Shortcode สำหรับแสดง Movie Section พร้อม View All Button
function modern_movie_section_shortcode($atts) {
    $atts = shortcode_atts(array(
        'title' => 'หนังใหม่',
        'view_all_url' => '#',
        'show_title' => 'true'
    ), $atts);

    ob_start();

    if ($atts['show_title'] === 'true') {
        echo '<div class="movie-section-header">';
        echo '<h2 class="movie-section-title">' . esc_html($atts['title']) . '</h2>';
        echo '<a href="' . esc_url($atts['view_all_url']) . '" class="modern-view-all-btn">';
        echo 'ดูทั้งหมด <span class="view-all-arrow">→</span>';
        echo '</a>';
        echo '</div>';
    }

    echo '<div class="movie-grid-container">';
    echo '<div class="movie-grid">';

    // ตัวอย่างการแสดงหนัง (ควรแทนที่ด้วยข้อมูลจริงจากฐานข้อมูล)
    display_modern_movie_grid();

    echo '</div>';
    echo '</div>';

    return ob_get_clean();
}
add_shortcode('modern_movie_section', 'modern_movie_section_shortcode');

// ฟังก์ชันแสดงหนังในรูปแบบ Grid (ตัวอย่าง)
function display_modern_movie_grid() {
    // ตัวอย่างข้อมูลหนัง - ควรแทนที่ด้วยข้อมูลจริงจากฐานข้อมูล
    $sample_movies = array(
        array(
            'title' => 'Hunting Grounds (2025)',
            'poster' => 'https://via.placeholder.com/200x300/1a1a1a/ffffff?text=Movie+1',
            'rating' => '8.1',
            'quality' => 'HD',
            'audio' => 'ซับไทย',
            'url' => '#'
        ),
        array(
            'title' => 'Undercover Inside the Bunker (2025)',
            'poster' => 'https://via.placeholder.com/200x300/1a1a1a/ffffff?text=Movie+2',
            'rating' => '8.7',
            'quality' => 'HD',
            'audio' => 'Soundtrack',
            'url' => '#'
        ),
        // เพิ่มหนังอื่นๆ ตามต้องการ
    );

    foreach ($sample_movies as $movie) {
        echo '<div class="movie-item">';
        echo '<a href="' . esc_url($movie['url']) . '">';
        echo '<div class="movie-poster">';
        echo '<img src="' . esc_url($movie['poster']) . '" alt="' . esc_attr($movie['title']) . '">';
        echo '<div class="movie-overlay">';
        echo '<div class="movie-rating">';
        echo '<span class="star-icon">★</span>' . esc_html($movie['rating']);
        echo '</div>';
        echo '<div class="movie-quality">' . esc_html($movie['quality']) . '</div>';
        echo '</div>';
        echo '</div>';
        echo '<div class="movie-info">';
        echo '<h3>' . esc_html($movie['title']) . '</h3>';
        echo '<p class="movie-audio">' . esc_html($movie['audio']) . '</p>';
        echo '</div>';
        echo '</a>';
        echo '</div>';
    }
}

?>
