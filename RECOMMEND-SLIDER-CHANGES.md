# 🎬 การแปลงหนังแนะนำเป็น Slider (อัปเดต)

## 📝 สรุปการเปลี่ยนแปลง

ได้แปลงส่วนหนังแนะนำในหน้าแรก (Under-HD ที่สุดของหนัง 2025) จากแสดงแบบ grid ธรรมดาเป็น **interactive slider** พร้อมลูกศรทันสมัยและการสไลด์ทีละ 8 เรื่อง

## 🔧 ไฟล์ที่แก้ไข

### 1. `index.php` 
**เปลี่ยนจาก:**
```html
<div class="grid-movie-advise">
    <!-- หนังแสดงแบบ grid ธรรมดา -->
</div>
```

**เป็น:**
```html
<div class="recommend-slider-wrapper">
    <button class="recommend-arrow recommend-prev">‹</button>
    <div class="recommend-slider">
        <div class="recommend-slides">
            <!-- หนังแสดงแบบ slider -->
        </div>
    </div>
    <button class="recommend-arrow recommend-next">›</button>
</div>
```

### 2. `functions.php`
**เพิ่ม:**
- CSS สำหรับ recommend slider (212 บรรทัด)
- JavaScript สำหรับการทำงาน (118 บรรทัด)
- ฟังก์ชัน `add_recommend_slider_styles()`
- ฟังก์ชัน `add_recommend_slider_script()`

## ✨ ฟีเจอร์ที่ได้

### 🎯 **Navigation**
- ลูกศรซ้าย-ขวาสำหรับเลื่อนดู
- ปุ่มจะ disabled เมื่อถึงจุดสุดท้าย
- รองรับการใช้ลูกศรบนคีย์บอร์ด

### 📱 **Touch Support**
- ปัดซ้าย-ขวาบนมือถือได้
- Responsive ปรับตามขนาดหน้าจอ

### 🎨 **Visual Effects**
- Hover effects สวยงาม
- Smooth transitions
- Rating และ quality badges
- Gradient overlays

### 📐 **การแสดงผล**
- **Desktop**: แสดง 8 หนังต่อหน้าจอ (ขนาดเดิม 200x320px)
- **Mobile**: แสดง 8 หนังต่อหน้าจอ (รักษาขนาดเดิม)
- **การสไลด์**: เลื่อนทีละ 8 เรื่อง (ชุดใหม่ทั้งหมด)
- **ลูกศร**: แบบทันสมัย (วงกลมใสพร้อมขอบ)

## 🎨 สไตล์ที่เพิ่ม

### **ลูกศรทันสมัย (Modern Arrows)**
```css
.recommend-arrow {
    background: rgba(0, 0, 0, 0.7);           /* พื้นหลังดำใส */
    border: 2px solid rgba(255, 255, 255, 0.3); /* ขอบขาวใส */
    border-radius: 50%;                        /* วงกลมสมบูรณ์ */
    width: 60px;                              /* ขนาดใหญ่ขึ้น */
    height: 60px;
    backdrop-filter: blur(10px);              /* เอฟเฟกต์เบลอ */
}

.recommend-arrow:hover {
    background: rgba(255, 107, 53, 0.9);     /* เปลี่ยนเป็นสีส้มเมื่อ hover */
    border-color: rgba(255, 255, 255, 0.8);  /* ขอบขาวชัดขึ้น */
    transform: scale(1.1);                    /* ขยายเล็กน้อย */
}
```

### **ขนาดหนัง (รักษาขนาดเดิม)**
```css
.recommend-slide-item {
    flex: 0 0 200px;     /* ความกว้าง 200px */
    height: 320px;       /* ความสูงรวม 320px */
    background: #1a1a1a;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.recommend-poster {
    height: 240px;       /* ส่วนโปสเตอร์ 240px */
}

.recommend-info {
    padding: 15px;       /* ส่วนข้อมูล 80px */
}
```

### **Hover Effects**
```css
.recommend-slide-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}
```

## 🔄 การทำงาน

### **เมื่อโหลดหน้า:**
1. แสดงหนังแนะนำ 16 เรื่อง (เพิ่มจาก 8 เรื่อง)
2. แสดงเฉพาะ 8 เรื่องแรกในหน้าจอ
3. ลูกศรซ้ายจะ disabled (เพราะอยู่จุดเริ่มต้น)

### **เมื่อคลิกลูกศร:**
1. เลื่อน slider ทีละ 8 เรื่อง (ชุดใหม่ทั้งหมด)
2. ชุดที่ 1: หนัง 1-8 → ชุดที่ 2: หนัง 9-16
3. อัพเดทสถานะของลูกศร
4. Smooth animation

### **เมื่อปัดบนมือถือ:**
1. ตรวจจับการปัด (threshold 50px)
2. เลื่อน slider ทีละ 8 เรื่อง
3. ป้องกันการปัดผิดพลาด

## 🎯 ข้อดีของการเปลี่ยนแปลง

### ✅ **ไม่กระทบโค้ดเดิม**
- ไม่ต้องสร้างไฟล์ใหม่
- ไม่ลบฟังก์ชันเดิม
- เพิ่มเติมเท่านั้น

### ✅ **ประสิทธิภาพดีขึ้น**
- แสดงหนังได้มากขึ้น (12 เรื่อง)
- ประหยัดพื้นที่หน้าจอ
- UX ดีขึ้น

### ✅ **Modern UI**
- ดูทันสมัยกว่าเดิม
- Interactive มากขึ้น
- Mobile-friendly

## 🔍 การทดสอบ

### **ควรทดสอบ:**
1. ✅ ลูกศรซ้าย-ขวาทำงาน
2. ✅ Responsive บนหน้าจอต่างขนาด
3. ✅ Touch/swipe บนมือถือ
4. ✅ Keyboard navigation
5. ✅ Hover effects
6. ✅ Loading performance

### **หากมีปัญหา:**
1. ตรวจสอบ Console errors
2. ดู CSS conflicts
3. ตรวจสอบ JavaScript loading

## 🎨 การปรับแต่งเพิ่มเติม

### **เปลี่ยนสี:**
```css
.recommend-arrow {
    background: linear-gradient(135deg, #your-color, #your-color-2);
}
```

### **เปลี่ยนขนาด:**
```css
.recommend-slide-item {
    flex: 0 0 250px; /* เปลี่ยนจาก 200px */
}
```

### **เปลี่ยนจำนวนหนัง:**
```php
// ใน index.php บรรทัด 23
$oz_press = new WP_Query("showposts=15&post_type=movie&category_name=recommend");
```

## 📞 หากต้องการความช่วยเหลือ

หากมีปัญหาหรือต้องการปรับแต่งเพิ่มเติม:
1. ตรวจสอบ Browser Console
2. ดู Network requests
3. ตรวจสอบ CSS conflicts
4. ทดสอบบนอุปกรณ์ต่างๆ
