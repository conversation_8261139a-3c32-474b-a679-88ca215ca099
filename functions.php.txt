<?php
add_action('after_switch_theme', function() {
    $siteSettings = get_option('mytheme_site_settings', array());
    $allowed = (isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types'])) ? $siteSettings['allowed_post_types'] : array();

    if (in_array('adult', $allowed)) {
        $adult_cats = [
            ['name' => 'AV Censored', 'slug' => 'av-censored'],
            ['name' => 'AV SubThai', 'slug' => 'av-subthai'],
            ['name' => 'AV Uncensored', 'slug' => 'av-uncensored'],
            ['name' => 'Erotic R18+', 'slug' => 'erotic-r18-plus'],
            ['name' => 'OnlyFan VIP', 'slug' => 'onlyfan-vip'],
            ['name' => 'Picpost AI', 'slug' => 'picpost-ai'],
            ['name' => 'Porn Video', 'slug' => 'porn-video']
        ];
        foreach ($adult_cats as $cat) {
            $byName = get_term_by('name', $cat['name'], 'adult_category');
            if ($byName && !is_wp_error($byName)) {
                if ($byName->slug !== $cat['slug']) {
                    wp_update_term($byName->term_id, 'adult_category', ['slug' => $cat['slug']]);
                }
                continue;
            }
            $bySlug = term_exists($cat['slug'], 'adult_category');
            if (!$bySlug) {
                wp_insert_term($cat['name'], 'adult_category', ['slug' => $cat['slug']]);
            }
        }
    }

    if (in_array('anime', $allowed)) {
        $anime_cats = [
            ['name' => 'อนิเมะญี่ปุ่น', 'slug' => 'anime-japanese'],
            ['name' => 'อนิเมะที่จบแล้ว', 'slug' => 'anime-finished'],
            ['name' => 'อนิเมะที่ออกอากาศ', 'slug' => 'anime-airing'],
            ['name' => 'อนิเมะฝรั่ง', 'slug' => 'anime-western'],
            ['name' => 'อนิเมะซับไทย', 'slug' => 'anime-subbed'],
            ['name' => 'อนิเมะพากย์ไทย', 'slug' => 'anime-dubbed'],
            ['name' => 'อนิเมะ OVA', 'slug' => 'anime-ova']
        ];
        foreach ($anime_cats as $cat) {
            $byName = get_term_by('name', $cat['name'], 'anime_category');
            if ($byName && !is_wp_error($byName)) {
                if ($byName->slug !== $cat['slug']) {
                    wp_update_term($byName->term_id, 'anime_category', ['slug' => $cat['slug']]);
                }
                continue;
            }
            $bySlug = term_exists($cat['slug'], 'anime_category');
            if (!$bySlug) {
                wp_insert_term($cat['name'], 'anime_category', ['slug' => $cat['slug']]);
            }
        }
    }

    if (in_array('serie', $allowed)) {
        $serie_cats = [
            ['name' => 'ซีรีส์ Netflix', 'slug' => 'series-netflix'],
            ['name' => 'ซีรีส์จีน', 'slug' => 'series-chinese'],
            ['name' => 'ซีรีส์ญี่ปุ่น', 'slug' => 'series-japanese'],
            ['name' => 'ซีรีส์ที่จบแล้ว', 'slug' => 'series-finished'],
            ['name' => 'ซีรีส์ที่ออกอากาศ', 'slug' => 'series-airing'],
            ['name' => 'ซีรีส์ฝรั่ง', 'slug' => 'series-western'],
            ['name' => 'ซีรีส์เกาหลี', 'slug' => 'series-korean'],
            ['name' => 'ซีรีส์แนะนำ', 'slug' => 'series-recommend'],
            ['name' => 'ซีรีส์ไทย', 'slug' => 'series-thai'],
            ['name' => 'ซีรีส์อินเดีย', 'slug' => 'series-india'],
            ['name' => 'ซีรีส์วาย', 'slug' => 'series-y']
        ];
        foreach ($serie_cats as $cat) {
            $byName = get_term_by('name', $cat['name'], 'serie_category');
            if ($byName && !is_wp_error($byName)) {
                if ($byName->slug !== $cat['slug']) {
                    wp_update_term($byName->term_id, 'serie_category', ['slug' => $cat['slug']]);
                }
                continue;
            }
            $bySlug = term_exists($cat['slug'], 'serie_category');
            if (!$bySlug) {
                wp_insert_term($cat['name'], 'serie_category', ['slug' => $cat['slug']]);
            }
        }
    }

    if (in_array('movie', $allowed)) {
        $movie_cats = [
            ['name' => 'หนังไทย', 'slug' => 'thai-movies'],
            ['name' => 'หนังฝรั่ง', 'slug' => 'western-movies'],
            ['name' => 'หนังเกาหลี', 'slug' => 'korean-movies'],
            ['name' => 'หนังจีน', 'slug' => 'chinese-movies'],
            ['name' => 'หนังญี่ปุ่น', 'slug' => 'japanese-movies'],
            ['name' => 'หนังอินเดีย', 'slug' => 'indian-movies'],
            ['name' => 'หนังรัสเซีย', 'slug' => 'russian-movies'],
            ['name' => 'หนังเอเชีย', 'slug' => 'asian-movies'],
            ['name' => 'หนังอินโดนีเซีย', 'slug' => 'indonesian-movies'],
            ['name' => 'หนังการ์ตูน', 'slug' => 'cartoon-movies'],
            ['name' => 'หนังภาคต่อ', 'slug' => 'sequel'],
            ['name' => 'การ์ตูนภาคต่อ', 'slug' => 'sequel-cartoon'],
            ['name' => 'หนัง DCU', 'slug' => 'dcu'],
            ['name' => 'หนัง Marvel', 'slug' => 'marvel'],
            ['name' => 'หนัง SuperHero', 'slug' => 'superhero'],
            ['name' => 'หนังซูมชนโรง', 'slug' => 'zoom'],
            ['name' => 'หนังเรทอาร์ 18+', 'slug' => 'erotic-18'],
            ['name' => 'หนังใหม่แนะนำ', 'slug' => 'recommend']
        ];
        foreach ($movie_cats as $cat) {
            $byName = get_term_by('name', $cat['name'], 'category');
            if ($byName && !is_wp_error($byName)) {
                if ($byName->slug !== $cat['slug']) {
                    wp_update_term($byName->term_id, 'category', ['slug' => $cat['slug']]);
                }
                continue;
            }
            $bySlug = term_exists($cat['slug'], 'category');
            if (!$bySlug) {
                wp_insert_term($cat['name'], 'category', ['slug' => $cat['slug']]);
            }
        }
    }

    create_extra_platforms();
});

add_action('after_switch_theme', function() {
    $existing_menus = wp_get_nav_menus();
    foreach ($existing_menus as $m) {
        wp_delete_term($m->term_id, 'nav_menu');
    }
    $sidebars_widgets = get_option('sidebars_widgets', ['wp_inactive_widgets' => [], 'array_version' => 4]);
    foreach ($sidebars_widgets as $sb => $items) {
        if (!in_array($sb, ['wp_inactive_widgets', 'array_version'])) {
            $sidebars_widgets[$sb] = [];
        }
    }
    update_option('sidebars_widgets', $sidebars_widgets);
    $json = '{
      "categories": [
        {"name": "Action", "slug": "action", "description": "", "parent_slug": ""},
        {"name": "Adventure", "slug": "adventure", "description": "", "parent_slug": ""},
        {"name": "Animation", "slug": "animation", "description": "", "parent_slug": ""},
        {"name": "Apple TV", "slug": "apple-tv", "description": "", "parent_slug": ""},
        {"name": "Biography", "slug": "biography", "description": "", "parent_slug": ""},
        {"name": "Comedy", "slug": "comedy", "description": "", "parent_slug": ""},
        {"name": "Crime", "slug": "crime", "description": "", "parent_slug": ""},
        {"name": "Disney Plus", "slug": "disney-plus", "description": "", "parent_slug": ""},
        {"name": "Documentary", "slug": "documentary", "description": "", "parent_slug": ""},
        {"name": "Drama", "slug": "drama", "description": "", "parent_slug": ""},
        {"name": "Family", "slug": "family", "description": "", "parent_slug": ""},
        {"name": "Fantasy", "slug": "fantasy", "description": "", "parent_slug": ""},
        {"name": "Game Show", "slug": "game-show", "description": "", "parent_slug": ""},
        {"name": "HBO Max", "slug": "hbo-max", "description": "", "parent_slug": ""},
        {"name": "History", "slug": "history", "description": "", "parent_slug": ""},
        {"name": "Horror", "slug": "horror", "description": "", "parent_slug": ""},
        {"name": "Hulu", "slug": "hulu", "description": "", "parent_slug": ""},
        {"name": "iQiYi", "slug": "iqiyi", "description": "", "parent_slug": ""},
        {"name": "Musical", "slug": "musical", "description": "", "parent_slug": ""},
        {"name": "Mystery", "slug": "mystery", "description": "", "parent_slug": ""},
        {"name": "NETFLIX", "slug": "netflix", "description": "", "parent_slug": ""},
        {"name": "Prime Video", "slug": "prime-video", "description": "", "parent_slug": ""},
        {"name": "Romance", "slug": "romance", "description": "", "parent_slug": ""},
        {"name": "Sci-Fi", "slug": "sci-fi", "description": "", "parent_slug": ""},
        {"name": "Serie", "slug": "serie", "description": "", "parent_slug": ""},
        {"name": "Sport", "slug": "sport", "description": "", "parent_slug": ""},
        {"name": "Super Hero", "slug": "super-hero", "description": "", "parent_slug": ""},
        {"name": "Thriller", "slug": "thriller", "description": "", "parent_slug": ""},
        {"name": "Uncategorized", "slug": "uncategorized", "description": "", "parent_slug": ""},
        {"name": "Viu", "slug": "viu", "description": "", "parent_slug": ""},
        {"name": "War", "slug": "war", "description": "", "parent_slug": ""},
        {"name": "Western", "slug": "western", "description": "", "parent_slug": ""},
        {"name": "WeTV", "slug": "wetv", "description": "", "parent_slug": ""},
        {"name": "Zombie", "slug": "zombie", "description": "", "parent_slug": ""}
      ],
      "menus": [
        {
          "name": "Genre",
          "slug": "genre",
          "items": [
            {"title": "Action", "type": "taxonomy", "object": "category", "children": [], "object_slug": "action"},
            {"title": "Adventure", "type": "taxonomy", "object": "category", "children": [], "object_slug": "adventure"},
            {"title": "Animation", "type": "taxonomy", "object": "category", "children": [], "object_slug": "animation"},
            {"title": "Biography", "type": "taxonomy", "object": "category", "children": [], "object_slug": "biography"},
            {"title": "Comedy", "type": "taxonomy", "object": "category", "children": [], "object_slug": "comedy"},
            {"title": "Crime", "type": "taxonomy", "object": "category", "children": [], "object_slug": "crime"},
            {"title": "Documentary", "type": "taxonomy", "object": "category", "children": [], "object_slug": "documentary"},
            {"title": "Drama", "type": "taxonomy", "object": "category", "children": [], "object_slug": "drama"},
            {"title": "Family", "type": "taxonomy", "object": "category", "children": [], "object_slug": "family"},
            {"title": "Fantasy", "type": "taxonomy", "object": "category", "children": [], "object_slug": "fantasy"},
            {"title": "Game Show", "type": "taxonomy", "object": "category", "children": [], "object_slug": "game-show"},
            {"title": "History", "type": "taxonomy", "object": "category", "children": [], "object_slug": "history"},
            {"title": "Horror", "type": "taxonomy", "object": "category", "children": [], "object_slug": "horror"},
            {"title": "Musical", "type": "taxonomy", "object": "category", "children": [], "object_slug": "musical"},
            {"title": "Mystery", "type": "taxonomy", "object": "category", "children": [], "object_slug": "mystery"},
            {"title": "Romance", "type": "taxonomy", "object": "category", "children": [], "object_slug": "romance"},
            {"title": "Sci-Fi", "type": "taxonomy", "object": "category", "children": [], "object_slug": "sci-fi"},
            {"title": "Super Hero", "type": "taxonomy", "object": "category", "children": [], "object_slug": "super-hero"},
            {"title": "Sport", "type": "taxonomy", "object": "category", "children": [], "object_slug": "sport"},
            {"title": "Thriller", "type": "taxonomy", "object": "category", "children": [], "object_slug": "thriller"},
            {"title": "War", "type": "taxonomy", "object": "category", "children": [], "object_slug": "war"},
            {"title": "Western", "type": "taxonomy", "object": "category", "children": [], "object_slug": "western"},
            {"title": "Zombie", "type": "taxonomy", "object": "category", "children": [], "object_slug": "zombie"}
          ]
        }
      ],
      "widgets": {
        "sidebarright": [
          {
            "id_base": "nav_menu",
            "settings": {
              "title": "หมวดหมู่",
              "nav_menu": 64,
              "menu": 64,
              "selected_menu_id": 64,
              "__nav_menu_export": {
                "menu_id": 64,
                "menu_slug": "category",
                "menu_name": "Category"
              }
            }
          }
        ],
        "sidebarleft": [
          {
            "id_base": "nav_menu",
            "settings": {
              "title": "ประเภท",
              "nav_menu": 65,
              "menu": 65,
              "selected_menu_id": 65,
              "__nav_menu_export": {
                "menu_id": 65,
                "menu_slug": "genre",
                "menu_name": "Genre"
              }
            }
          }
        ]
      }
    }';
    $data = json_decode($json, true);
    if (!empty($data['categories']) && !empty($data['menus']) && !empty($data['widgets'])) {
        import_site_data_no_duplicate($data);
    }
});

function import_site_data_no_duplicate($data) {
    $siteSettings = get_option('mytheme_site_settings', array());
    $allowed_types = (isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types'])) ? $siteSettings['allowed_post_types'] : array();

    if (!empty($data['categories']) && is_array($data['categories'])) {
        foreach ($data['categories'] as $cat) {
            if (!empty($cat['slug']) && !empty($cat['name'])) {
                $existingByName = wp_get_nav_menu_object($cat['name']);
                $termByName = get_term_by('name', $cat['name'], 'category');
                if ($termByName && !is_wp_error($termByName)) {
                    if ($termByName->slug !== $cat['slug']) {
                        wp_update_term($termByName->term_id, 'category', ['slug' => $cat['slug']]);
                    }
                    continue;
                }
                $existingBySlug = term_exists($cat['slug'], 'category');
                if (!$existingBySlug) {
                    wp_insert_term($cat['name'], 'category', ['slug' => $cat['slug']]);
                }
            }
        }
    }

    $menus = wp_get_nav_menus();
    if (!empty($data['menus']) && is_array($data['menus'])) {
        foreach ($data['menus'] as $menuData) {
            if ($menuData['slug'] === 'category') {
                continue;
            }
            if (!empty($menuData['name']) && !empty($menuData['slug'])) {
                $found = null;
                foreach ($menus as $exMenu) {
                    if ($exMenu->name === $menuData['name']) {
                        wp_update_nav_menu_object($exMenu->term_id, ['slug' => $menuData['slug']]);
                        $found = $exMenu->term_id;
                        break;
                    }
                }
                if (!$found) {
                    $bySlug = wp_get_nav_menu_object($menuData['slug']);
                    if ($bySlug && !is_wp_error($bySlug)) {
                        wp_update_nav_menu_object($bySlug->term_id, ['name' => $menuData['name']]);
                        $found = $bySlug->term_id;
                    }
                }
                if (!$found) {
                    $newMenu = wp_create_nav_menu($menuData['name']);
                    if (is_wp_error($newMenu)) {
                        continue;
                    }
                    wp_update_nav_menu_object($newMenu, ['slug' => $menuData['slug']]);
                    $found = $newMenu;
                }
                $menu_id = $found;
                $index = 0;
                $addItems = function($items, $parentId = 0) use (&$addItems, $menu_id, &$index) {
                    foreach ($items as $it) {
                        $index++;
                        $params = [
                            'menu-item-title' => $it['title'],
                            'menu-item-parent-id' => $parentId,
                            'menu-item-status' => 'publish',
                            'menu-item-position' => $index
                        ];
                        if (!empty($it['type'])) {
                            if ($it['type'] === 'custom') {
                                $params['menu-item-type'] = 'custom';
                                $params['menu-item-url'] = !empty($it['url']) ? $it['url'] : '#';
                            } elseif ($it['type'] === 'taxonomy' && !empty($it['object']) && $it['object'] === 'category') {
                                if (!empty($it['object_slug'])) {
                                    $termByName = get_term_by('name', $it['title'], 'category');
                                    if ($termByName && !is_wp_error($termByName)) {
                                        if ($termByName->slug !== $it['object_slug']) {
                                            wp_update_term($termByName->term_id, 'category', ['slug' => $it['object_slug']]);
                                        }
                                        $params['menu-item-type'] = 'taxonomy';
                                        $params['menu-item-object'] = 'category';
                                        $params['menu-item-object-id'] = $termByName->term_id;
                                    } else {
                                        $termBySlug = get_term_by('slug', $it['object_slug'], 'category');
                                        if (!$termBySlug) {
                                            $newCat = wp_insert_term($it['title'], 'category', ['slug' => $it['object_slug']]);
                                            if (!is_wp_error($newCat)) {
                                                $termBySlug = get_term_by('id', $newCat['term_id'], 'category');
                                            }
                                        }
                                        if ($termBySlug) {
                                            $params['menu-item-type'] = 'taxonomy';
                                            $params['menu-item-object'] = 'category';
                                            $params['menu-item-object-id'] = $termBySlug->term_id;
                                        } else {
                                            continue;
                                        }
                                    }
                                } else {
                                    continue;
                                }
                            }
                        }
                        $new_item_id = wp_update_nav_menu_item($menu_id, 0, $params);
                        if (!is_wp_error($new_item_id)) {
                            if (!empty($it['children']) && is_array($it['children'])) {
                                $addItems($it['children'], $new_item_id);
                            }
                        }
                    }
                };
                $addItems($menuData['items'], 0);
                if (!empty($menuData['locations']) && is_array($menuData['locations'])) {
                    $locs = get_theme_mod('nav_menu_locations');
                    if (!is_array($locs)) {
                        $locs = [];
                    }
                    foreach ($menuData['locations'] as $lc) {
                        $locs[$lc] = $menu_id;
                    }
                    set_theme_mod('nav_menu_locations', $locs);
                }
            }
        }
    }

    if (!empty($data['widgets']) && is_array($data['widgets'])) {
        global $wp_registered_sidebars;
        $avs = !empty($wp_registered_sidebars) ? wp_list_pluck($wp_registered_sidebars, 'name', 'id') : [];
        $sidebars_widgets = get_option('sidebars_widgets', ['wp_inactive_widgets' => [], 'array_version' => 4]);
        foreach ($data['widgets'] as $sb => $items) {
            if (!is_array($items)) {
                continue;
            }
            $target = isset($avs[$sb]) ? $sb : 'wp_inactive_widgets';
            if (!isset($sidebars_widgets[$target])) {
                $sidebars_widgets[$target] = [];
            }
            foreach ($items as $w) {
                if (empty($w['id_base']) || !isset($w['settings'])) {
                    continue;
                }
                $id_base = $w['id_base'];
                $sett = $w['settings'];
                if ($id_base === 'nav_menu' && !empty($sett['__nav_menu_export']['menu_slug'])) {
                    $mf = wp_get_nav_menu_object($sett['__nav_menu_export']['menu_slug']);
                    unset($sett['__nav_menu_export']);
                    if ($mf && !is_wp_error($mf)) {
                        $sett['nav_menu'] = $mf->term_id;
                        $sett['menu'] = $mf->term_id;
                        $sett['selected_menu_id'] = $mf->term_id;
                    }
                }
                $opt_name = 'widget_' . $id_base;
                $wd = get_option($opt_name);
                if (!is_array($wd)) {
                    $wd = [];
                }
                $mnum = isset($wd['_multiwidget']) ? $wd['_multiwidget'] : 0;
                if (isset($wd['_multiwidget'])) {
                    unset($wd['_multiwidget']);
                }
                $idx = 1;
                $ks = array_filter(array_keys($wd), 'is_numeric');
                if (!empty($ks)) {
                    $idx = max($ks) + 1;
                }
                $wd[$idx] = $sett;
                if ($mnum) {
                    $wd['_multiwidget'] = $mnum;
                }
                update_option($opt_name, $wd);
                $widget_id = $id_base . '-' . $idx;
                $sidebars_widgets[$target][] = $widget_id;
            }
        }
        update_option('sidebars_widgets', $sidebars_widgets);
    }
    create_separate_menus_for_allowed_types($allowed_types);
    return true;
}

function create_extra_platforms() {
    $extra = [
        ['name' => 'Apple TV', 'slug' => 'apple-tv'],
        ['name' => 'Disney Plus', 'slug' => 'disney-plus'],
        ['name' => 'HBO Max', 'slug' => 'hbo-max'],
        ['name' => 'Hulu', 'slug' => 'hulu'],
        ['name' => 'iQiYi', 'slug' => 'iqiyi'],
        ['name' => 'Netflix', 'slug' => 'netflix'],
        ['name' => 'Prime Video', 'slug' => 'prime-video'],
        ['name' => 'Viu', 'slug' => 'viu'],
        ['name' => 'WeTV', 'slug' => 'wetv']
    ];
    foreach ($extra as $cat) {
        $byName = get_term_by('name', $cat['name'], 'category');
        if ($byName && !is_wp_error($byName)) {
            if ($byName->slug !== $cat['slug']) {
                wp_update_term($byName->term_id, 'category', ['slug' => $cat['slug']]);
            }
            continue;
        }
        $bySlug = term_exists($cat['slug'], 'category');
        if (!$bySlug) {
            wp_insert_term($cat['name'], 'category', ['slug' => $cat['slug']]);
        }
    }
}

function create_separate_menus_for_allowed_types($allowed_types) {
    if (in_array('movie', $allowed_types)) {
        $ex = wp_get_nav_menu_object('movie-menu');
        if ($ex) {
            wp_delete_term($ex->term_id, 'nav_menu');
        }
        $mID = wp_create_nav_menu('Movie', ['slug' => 'movie-menu']);
        if (!is_wp_error($mID)) {
            $catTerms = get_terms(['taxonomy' => 'category','hide_empty' => false]);
            $extraList = ['netflix','apple-tv','disney-plus','hbo-max','hulu','iqiyi','prime-video','viu','wetv'];
            $movieList = [
                'thai-movies','western-movies','korean-movies','chinese-movies','japanese-movies','indian-movies',
                'russian-movies','asian-movies','indonesian-movies','cartoon-movies','sequel','sequel-cartoon',
                'dcu','marvel','superhero','zoom','erotic-18','recommend'
            ];
            $pos = 0;
            foreach ($extraList as $exSlug) {
                $pos++;
                $t = get_term_by('slug', $exSlug, 'category');
                if ($t) {
                    wp_update_nav_menu_item($mID, 0, [
                        'menu-item-title' => $t->name,
                        'menu-item-type' => 'taxonomy',
                        'menu-item-object' => 'category',
                        'menu-item-object-id' => $t->term_id,
                        'menu-item-status' => 'publish',
                        'menu-item-position' => $pos
                    ]);
                }
            }
            if (!is_wp_error($catTerms)) {
                foreach ($catTerms as $ct) {
                    if (in_array($ct->slug, $movieList)) {
                        $pos++;
                        wp_update_nav_menu_item($mID, 0, [
                            'menu-item-title' => $ct->name,
                            'menu-item-type' => 'taxonomy',
                            'menu-item-object' => 'category',
                            'menu-item-object-id' => $ct->term_id,
                            'menu-item-status' => 'publish',
                            'menu-item-position' => $pos
                        ]);
                    }
                }
            }
        }
    }
    if (in_array('serie', $allowed_types)) {
        $ex2 = wp_get_nav_menu_object('serie-menu');
        if ($ex2) {
            wp_delete_term($ex2->term_id, 'nav_menu');
        }
        $mID2 = wp_create_nav_menu('Serie', ['slug' => 'serie-menu']);
        if (!is_wp_error($mID2)) {
            $serieTerms = get_terms(['taxonomy' => 'serie_category','hide_empty' => false]);
            $exSlug2 = ['netflix','apple-tv','disney-plus','hbo-max','hulu','iqiyi','prime-video','viu','wetv'];
            $p2 = 0;
            if (!in_array('movie', $allowed_types)) {
                foreach ($exSlug2 as $xx) {
                    $p2++;
                    $tt = get_term_by('slug', $xx, 'category');
                    if ($tt) {
                        wp_update_nav_menu_item($mID2, 0, [
                            'menu-item-title' => $tt->name,
                            'menu-item-type' => 'taxonomy',
                            'menu-item-object' => 'category',
                            'menu-item-object-id' => $tt->term_id,
                            'menu-item-status' => 'publish',
                            'menu-item-position' => $p2
                        ]);
                    }
                }
            }
            foreach ($serieTerms as $st) {
                $p2++;
                wp_update_nav_menu_item($mID2, 0, [
                    'menu-item-title' => $st->name,
                    'menu-item-type' => 'taxonomy',
                    'menu-item-object' => 'serie_category',
                    'menu-item-object-id' => $st->term_id,
                    'menu-item-status' => 'publish',
                    'menu-item-position' => $p2
                ]);
            }
        }
    }
    if (in_array('anime', $allowed_types)) {
        $ex3 = wp_get_nav_menu_object('anime-menu');
        if ($ex3) {
            wp_delete_term($ex3->term_id, 'nav_menu');
        }
        $mID3 = wp_create_nav_menu('Anime', ['slug' => 'anime-menu']);
        if (!is_wp_error($mID3)) {
            $animeTerms = get_terms(['taxonomy' => 'anime_category','hide_empty' => false]);
            $p3 = 0;
            foreach ($animeTerms as $at) {
                $p3++;
                wp_update_nav_menu_item($mID3, 0, [
                    'menu-item-title' => $at->name,
                    'menu-item-type' => 'taxonomy',
                    'menu-item-object' => 'anime_category',
                    'menu-item-object-id' => $at->term_id,
                    'menu-item-status' => 'publish',
                    'menu-item-position' => $p3
                ]);
            }
        }
    }
    if (in_array('adult', $allowed_types)) {
        $ex4 = wp_get_nav_menu_object('adult-menu');
        if ($ex4) {
            wp_delete_term($ex4->term_id, 'nav_menu');
        }
        $mID4 = wp_create_nav_menu('Adult', ['slug' => 'adult-menu']);
        if (!is_wp_error($mID4)) {
            $adultTerms = get_terms(['taxonomy' => 'adult_category','hide_empty' => false]);
            $p4 = 0;
            foreach ($adultTerms as $adt) {
                $p4++;
                wp_update_nav_menu_item($mID4, 0, [
                    'menu-item-title' => $adt->name,
                    'menu-item-type' => 'taxonomy',
                    'menu-item-object' => 'adult_category',
                    'menu-item-object-id' => $adt->term_id,
                    'menu-item-status' => 'publish',
                    'menu-item-position' => $p4
                ]);
            }
        }
    }
}

add_action('after_switch_theme', 'create_custom_pages_on_theme_activation');
function create_custom_pages_on_theme_activation() {
    $siteSettings = get_option('mytheme_site_settings', array());
    $allowed_types = (isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types'])) ? $siteSettings['allowed_post_types'] : array();
    $pages = [
        ['title' => 'Animes', 'slug' => 'animes', 'template' => 'page-animes.php', 'allowed' => ['anime']],
        ['title' => 'Movies', 'slug' => 'movies', 'template' => 'page-movies.php', 'allowed' => ['movie']],
        ['title' => 'PlayerMovie', 'slug' => 'playermovie', 'template' => 'page-playermovie.php', 'allowed' => ['movie']],
        ['title' => 'PlayerSerie', 'slug' => 'playerserie', 'template' => 'page-playerserie.php', 'allowed' => ['serie']],
        ['title' => 'PlayerAdult', 'slug' => 'playeradult', 'template' => 'page-playeradult.php', 'allowed' => ['adult']],
        ['title' => 'Popular', 'slug' => 'popular', 'template' => 'page-popular.php', 'allowed' => ['movie','serie','anime','adult']],
        ['title' => 'Series', 'slug' => 'series', 'template' => 'page-series.php', 'allowed' => ['serie']],
        ['title' => 'Top IMDB', 'slug' => 'top-imdb', 'template' => 'page-top-imdb.php', 'allowed' => ['movie','serie']],
        ['title' => 'Adult', 'slug' => 'adults', 'template' => 'page-adults.php', 'allowed' => ['adult']]
    ];
    $filtered_pages = array();
    foreach ($pages as $page) {
        if (!empty($page['allowed']) && is_array($page['allowed'])) {
            if (empty(array_intersect($page['allowed'], $allowed_types))) {
                continue;
            }
        }
        $filtered_pages[] = $page;
    }
    $theme_dir = get_stylesheet_directory();
    $content_page_dir = $theme_dir . '/Content-Page';
    if (!file_exists($content_page_dir)) {
        mkdir($content_page_dir, 0755, true);
    }
    $allowed_templates = array();
    $allowed_slugs = array();
    foreach ($filtered_pages as $page) {
        $allowed_templates[] = $page['template'];
        $allowed_slugs[] = $page['slug'];
    }
    foreach ($filtered_pages as $page) {
        $template_path = $content_page_dir . '/' . $page['template'];
        if (!file_exists($template_path) && is_writable($content_page_dir)) {
            file_put_contents($template_path, "<?php\n\nget_header();\n\n?>\n<div class=\"content\">\n    <h1>" . $page['title'] . "</h1>\n    <p>Content for " . $page['title'] . "</p>\n</div>\n\n<?php\n\nget_footer();\n\n?>");
        }
        if (!get_page_by_title($page['title']) && !get_page_by_path($page['slug'])) {
            $new_page_id = wp_insert_post([
                'post_title'  => $page['title'],
                'post_name'   => $page['slug'],
                'post_status' => 'publish',
                'post_type'   => 'page'
            ]);
            if ($new_page_id && !is_wp_error($new_page_id)) {
                update_post_meta($new_page_id, '_wp_page_template', 'Content-Page/' . $page['template']);
            }
        }
    }
    $all_pages = get_pages();
    foreach ($all_pages as $page_obj) {
        $page_template = get_post_meta($page_obj->ID, '_wp_page_template', true);
        $page_slug = $page_obj->post_name;
        if (strpos($page_template, 'Content-Page/') === 0) {
            $template_file = basename($page_template);
            if (!in_array($template_file, $allowed_templates)) {
                wp_delete_post($page_obj->ID, true);
                continue;
            }
        }
        if (!in_array($page_slug, $allowed_slugs)) {
            wp_delete_post($page_obj->ID, true);
        }
    }
    $files = scandir($content_page_dir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            if (!in_array($file, $allowed_templates)) {
                $file_path = $content_page_dir . '/' . $file;
                if (is_file($file_path)) {
                    unlink($file_path);
                }
            }
        }
    }
    flush_rewrite_rules();
}

add_action('after_switch_theme', 'create_custom_single_templates_on_theme_activation');
function create_custom_single_templates_on_theme_activation() {
    $siteSettings = get_option('mytheme_site_settings', array());
    $allowed_types = (isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types'])) ? $siteSettings['allowed_post_types'] : array();
    $singles = [
        ['slug' => 'movie', 'template' => 'single-movie.php', 'allowed' => ['movie']],
        ['slug' => 'serie', 'template' => 'single-serie.php', 'allowed' => ['serie']],
        ['slug' => 'anime', 'template' => 'single-anime.php', 'allowed' => ['anime']],
        ['slug' => 'adult', 'template' => 'single-adult.php', 'allowed' => ['adult']]
    ];
    $filtered_singles = array();
    foreach ($singles as $s) {
        if (!empty($s['allowed']) && is_array($s['allowed'])) {
            if (empty(array_intersect($s['allowed'], $allowed_types))) {
                continue;
            }
        }
        $filtered_singles[] = $s;
    }
    $theme_dir = get_stylesheet_directory();
    $content_single_dir = $theme_dir . '/Content-Single';
    if (!file_exists($content_single_dir)) {
        mkdir($content_single_dir, 0755, true);
    }
    $allowed_templates = array();
    foreach ($filtered_singles as $s) {
        $allowed_templates[] = $s['template'];
    }
    foreach ($filtered_singles as $s) {
        $template_path = $content_single_dir . '/' . $s['template'];
        if (!file_exists($template_path) && is_writable($content_single_dir)) {
            file_put_contents($template_path, "<?php\n\nget_header();\n\n?>\n<div class=\"content-single\">\n    <h1>Single for ".$s['slug']."</h1>\n    <p>Content for ".$s['slug']."</p>\n</div>\n\n<?php\n\nget_footer();\n\n?>");
        }
    }
    $files = scandir($content_single_dir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            if (!in_array($file, $allowed_templates)) {
                $file_path = $content_single_dir . '/' . $file;
                if (is_file($file_path)) {
                    unlink($file_path);
                }
            }
        }
    }
    flush_rewrite_rules();
}

function maybe_create_years_and_imdb_terms() {
    $siteSettings = get_option('mytheme_site_settings', array());
    $allowed = (isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types'])) ? $siteSettings['allowed_post_types'] : array();
    if (in_array('movie', $allowed) || in_array('serie', $allowed)) {
        if (function_exists('create_years_and_imdb_terms_on_plugin_activation')) {
            create_years_and_imdb_terms_on_plugin_activation();
        }
    }
}
add_action('after_switch_theme', 'maybe_create_years_and_imdb_terms');

function custom_header_css() {
    ?>
    <style>
    .oncount {
        justify-content: space-between !important;
        display: flex;
    }
    .sidebar ul li .post-count {
        padding-right: 20px;
    }
    </style>
    <?php
}
add_action('wp_head', 'custom_header_css');

class Custom_Menu_Widget extends WP_Widget {
    public function __construct() {
        parent::__construct(
            'custom_menu_widget',
            'Custom Menu Widget',
            array('description' => 'A widget that displays a menu with an optional post count.')
        );
    }

    public function widget($args, $instance) {
        $menu_id = !empty($instance['menu_id']) ? $instance['menu_id'] : '';
        $show_count = !empty($instance['show_count']) ? $instance['show_count'] : false;
        echo $args['before_widget'];
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        if (!empty($menu_id)) {
            $menu_obj = wp_get_nav_menu_object($menu_id);
            $menu_name_class = $menu_obj ? sanitize_title($menu_obj->name) : '';
            $menu_items = wp_get_nav_menu_items($menu_id);
            if ($menu_items) {
                echo '<ul class="custom-menu-widget ' . esc_attr($menu_name_class) . '">';
                foreach ($menu_items as $menu_item) {
                    $category = ($menu_item->object == 'category') ? get_category($menu_item->object_id) : null;
                    $post_count = ($show_count && $category) ? ' <span class="post-count">(' . $category->count . ')</span>' : '';
                    $li_classes = 'cat-item cat-item-' . $menu_item->ID;
                    if ($show_count && $menu_item->object == 'category') {
                        $li_classes .= ' oncount';
                    }
                    echo '<li class="' . $li_classes . '">';
                    echo '<a href="' . esc_url($menu_item->url) . '">' . esc_html($menu_item->title) . '</a>' . $post_count;
                    echo '</li>';
                }
                echo '</ul>';
            } else {
                echo 'No menu items found.';
            }
        } else {
            echo 'Please select a menu in the widget settings.';
        }
        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = isset($instance['title']) ? $instance['title'] : 'Menu';
        $menu_id = isset($instance['menu_id']) ? $instance['menu_id'] : '';
        $show_count = isset($instance['show_count']) ? (bool)$instance['show_count'] : false;
        $menus = wp_get_nav_menus();
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Title:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('menu_id'); ?>">Select Menu:</label>
            <select class="widefat" id="<?php echo $this->get_field_id('menu_id'); ?>" name="<?php echo $this->get_field_name('menu_id'); ?>">
                <option value="">-- Select Menu --</option>
                <?php foreach ($menus as $menu) { ?>
                    <option value="<?php echo esc_attr($menu->term_id); ?>" <?php selected($menu_id, $menu->term_id); ?>>
                        <?php echo esc_html($menu->name); ?>
                    </option>
                <?php } ?>
            </select>
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_count); ?> id="<?php echo $this->get_field_id('show_count'); ?>" name="<?php echo $this->get_field_name('show_count'); ?>" />
            <label for="<?php echo $this->get_field_id('show_count'); ?>">Show Post Count</label>
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        $instance['menu_id'] = (!empty($new_instance['menu_id'])) ? strip_tags($new_instance['menu_id']) : '';
        $instance['show_count'] = isset($new_instance['show_count']) ? (bool)$new_instance['show_count'] : false;
        return $instance;
    }
}

function register_custom_menu_widget() {
    register_widget('Custom_Menu_Widget');
}
add_action('widgets_init', 'register_custom_menu_widget');

function add_file_types_to_uploads($file_types){
$new_filetypes = array();
$new_filetypes['svg'] = 'image/svg+xml';
$file_types = array_merge($file_types, $new_filetypes );
return $file_types;
}
add_filter('upload_mimes', 'add_file_types_to_uploads');

// Include Movie Slider functionality
require_once get_template_directory() . '/movie-slider.php';

// Enqueue Movie Slider assets
function enqueue_movie_slider_assets() {
    wp_enqueue_style('movie-slider-css', get_template_directory_uri() . '/movie-slider.css', array(), '1.0.0');
    wp_enqueue_script('movie-slider-js', get_template_directory_uri() . '/movie-slider.js', array(), '1.0.0', true);

    // Localize script for AJAX
    wp_localize_script('movie-slider-js', 'movieSliderAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('movie_slider_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_movie_slider_assets');

// Shortcode สำหรับแสดง Movie Slider
function movie_slider_shortcode($atts) {
    $atts = shortcode_atts(array(
        'title' => 'หนังใหม่',
        'show_title' => 'true'
    ), $atts);

    ob_start();

    if ($atts['show_title'] === 'true') {
        echo '<div class="slider-section-title">';
        echo '<h2>' . esc_html($atts['title']) . '</h2>';
        echo '<a href="#" class="view-all-link">ดูทั้งหมด <span class="arrow">→</span></a>';
        echo '</div>';
    }

    display_movie_slider();

    return ob_get_clean();
}
add_shortcode('movie_slider', 'movie_slider_shortcode');

// Add Recommend Slider CSS and JS
function add_recommend_slider_styles() {
    ?>
    <style>
    /* Recommend Movies Slider */
    .recommend-slider-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        gap: 15px;
        margin: 20px 0;
    }

    .recommend-arrow {
        background: rgba(0, 0, 0, 0.7);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        z-index: 10;
        position: relative;
    }

    .recommend-arrow:hover {
        background: rgba(255, 107, 53, 0.9);
        border-color: rgba(255, 255, 255, 0.8);
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
    }

    .recommend-arrow:disabled {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.1);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        opacity: 0.5;
    }

    .recommend-arrow .arrow-icon {
        font-size: 20px;
        font-weight: bold;
        color: white;
        line-height: 1;
    }

    .recommend-slider {
        flex: 1;
        overflow: hidden;
        border-radius: 10px;
        width: calc(8 * 215px); /* แสดง 8 เรื่อง: 8 * (200px + 15px gap) */
        max-width: 100%;
    }

    .recommend-slides {
        display: flex;
        gap: 15px;
        transition: transform 0.4s ease;
        padding: 10px 0;
    }

    .recommend-slide-item {
        flex: 0 0 200px;
        background: #1a1a1a;
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        height: 320px;
    }

    .recommend-slide-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    }

    .recommend-slide-item a {
        display: block;
        text-decoration: none;
        color: inherit;
    }

    .recommend-poster {
        position: relative;
        width: 100%;
        height: 240px;
        overflow: hidden;
    }

    .recommend-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .recommend-slide-item:hover .recommend-poster img {
        transform: scale(1.05);
    }

    .recommend-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent, rgba(0,0,0,0.7));
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .recommend-slide-item:hover .recommend-overlay {
        opacity: 1;
    }

    .recommend-rating {
        display: flex;
        align-items: center;
        gap: 5px;
        background: rgba(255, 107, 53, 0.9);
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        align-self: flex-start;
    }

    .recommend-rating .star-icon {
        color: #ffd700;
    }

    .recommend-quality {
        background: rgba(0, 0, 0, 0.8);
        color: #00ff00;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        font-weight: bold;
        align-self: flex-end;
    }

    .recommend-info {
        padding: 15px;
        background: #1a1a1a;
    }

    .recommend-info h3 {
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        margin: 0 0 8px 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .recommend-audio {
        font-size: 12px;
        color: #ff6b35;
        font-weight: 500;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .recommend-slide-item {
            flex: 0 0 200px; /* รักษาขนาดเดิม */
            height: 320px;   /* รักษาขนาดเดิม */
        }

        .recommend-poster {
            height: 240px;   /* รักษาขนาดเดิม */
        }

        .recommend-arrow {
            width: 50px;     /* รักษาขนาดเดิม */
            height: 50px;    /* รักษาขนาดเดิม */
        }

        .recommend-arrow .arrow-icon {
            font-size: 18px;
        }

        .recommend-slider-wrapper {
            gap: 10px; /* ลดระยะห่างเล็กน้อยบนมือถือ */
        }
    }

    @media (max-width: 480px) {
        .recommend-slide-item {
            flex: 0 0 200px; /* รักษาขนาดเดิม */
            height: 320px;   /* รักษาขนาดเดิม */
        }

        .recommend-poster {
            height: 240px;   /* รักษาขนาดเดิม */
        }

        .recommend-info {
            padding: 15px;   /* รักษาขนาดเดิม */
        }

        .recommend-info h3 {
            font-size: 14px; /* รักษาขนาดเดิม */
        }

        .recommend-slider-wrapper {
            gap: 8px; /* ลดระยะห่างเล็กน้อยบนมือถือเล็ก */
        }
    }
    </style>
    <?php
}
add_action('wp_head', 'add_recommend_slider_styles');

// Add Recommend Slider JavaScript
function add_recommend_slider_script() {
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const recommendSlider = document.querySelector('.recommend-slides');
        const prevBtn = document.querySelector('.recommend-prev');
        const nextBtn = document.querySelector('.recommend-next');

        if (!recommendSlider || !prevBtn || !nextBtn) return;

        let currentSlide = 0;
        const moviesPerSlide = 8; // แสดง 8 เรื่องต่อหน้าจอ
        const slideWidth = 215; // 200px + 15px gap

        function getTotalSlides() {
            return recommendSlider.querySelectorAll('.recommend-slide-item').length;
        }

        function getTotalSlideGroups() {
            const totalMovies = getTotalSlides();
            return Math.ceil(totalMovies / moviesPerSlide);
        }

        function updateSliderPosition() {
            const translateX = -currentSlide * (slideWidth * moviesPerSlide);
            recommendSlider.style.transform = `translateX(${translateX}px)`;
        }

        function updateArrowStates() {
            const totalGroups = getTotalSlideGroups();

            prevBtn.disabled = currentSlide <= 0;
            nextBtn.disabled = currentSlide >= (totalGroups - 1);

            // Update visual states
            prevBtn.style.opacity = currentSlide <= 0 ? '0.5' : '1';
            nextBtn.style.opacity = currentSlide >= (totalGroups - 1) ? '0.5' : '1';
        }

        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                updateSliderPosition();
                updateArrowStates();
            }
        }

        function nextSlide() {
            const totalGroups = getTotalSlideGroups();

            if (currentSlide < (totalGroups - 1)) {
                currentSlide++;
                updateSliderPosition();
                updateArrowStates();
            }
        }

        // Event listeners
        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevSlide();
            if (e.key === 'ArrowRight') nextSlide();
        });

        // Touch support
        let startX = 0;
        let currentX = 0;
        let isDragging = false;

        recommendSlider.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            isDragging = true;
        });

        recommendSlider.addEventListener('touchmove', function(e) {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });

        recommendSlider.addEventListener('touchend', function() {
            if (!isDragging) return;

            const diffX = startX - currentX;
            const threshold = 50;

            if (Math.abs(diffX) > threshold) {
                if (diffX > 0) {
                    nextSlide();
                } else {
                    prevSlide();
                }
            }

            isDragging = false;
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            // Keep current slide position on resize
            updateSliderPosition();
            updateArrowStates();
        });

        // Initialize
        updateArrowStates();
    });
    </script>
    <?php
}
add_action('wp_footer', 'add_recommend_slider_script');

function disable_jquery_on_frontend() {
    if (!is_admin()) {
        wp_deregister_script('jquery');
    }
}
add_action('wp_enqueue_scripts', 'disable_jquery_on_frontend');

function remove_block_library_css() {
    if (!is_admin()) {
        wp_dequeue_style('wp-block-library');
        wp_dequeue_style('wp-block-library-theme');
        wp_dequeue_style('wc-block-style');
    }
}
add_action('wp_enqueue_scripts', 'remove_block_library_css', 100);

add_filter('show_admin_bar', '__return_false');

function get_audio_type($post_id) {
    $post_type = get_post_type($post_id);
    if ($post_type === 'serie' || $post_type === 'anime' ) {
        $dubbed = get_post_meta($post_id, 'serie_dubbed', true);
        $subbed = get_post_meta($post_id, 'serie_subbed', true);
    } else {
        $dubbed = get_post_meta($post_id, 'gdrivethai', true);
        $subbed = get_post_meta($post_id, 'gdrivesub', true);
    }
    if (!empty($dubbed) && !empty($subbed)) {
        echo 'พากย์ไทย/ซับไทย';
    } elseif (!empty($dubbed)) {
        echo 'พากย์ไทย';
    } elseif (!empty($subbed)) {
        echo 'ซับไทย';
    } else {
        echo 'ไม่มีข้อมูลเสียง';
    }
}

function display_english_title($post_id) {
    list($english_title, $thai_title, $year) = split_title($post_id);
    if (empty($english_title)) {
        return $thai_title . ' (' . $year . ')';
    }
    return $english_title . ' (' . $year . ')';
}

function get_video_sources_by_post_id($postID) {
    $gdrivedubbed = get_post_meta($postID, 'gdrivethai', true);
    $gdrivesubbed = get_post_meta($postID, 'gdrivesub', true);
    $results = array('drivethais' => null, 'drivesubs' => null);
    if (!empty($gdrivedubbed)) {
        $data_dub = array('source' => $gdrivedubbed, 'key' => '@!Qazwsx2531');
        $results['drivethais'] = get_cached_transformed_url($data_dub, $postID, null, 'dubbed');
    }
    if (!empty($gdrivesubbed)) {
        $data_sub = array('source' => $gdrivesubbed, 'key' => '@!Qazwsx2531');
        $results['drivesubs'] = get_cached_transformed_url($data_sub, $postID, null, 'subbed');
    }
    return $results;
}

add_action('rest_api_init', function () {
    register_rest_route('securevideo/v1', '/getvideourl/', array(
        'methods' => 'GET',
        'callback' => 'get_video_url',
        'args' => array(
            'postID' => array(
                'required' => true,
                'validate_callback' => function ($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
            'type' => array(
                'required' => false,
                'validate_callback' => function ($param, $request, $key) {
                    return empty($param) || in_array($param, ['dubbed', 'subbed']);
                }
            )
        ),
        'permission_callback' => '__return_true'
    ));
});

function get_video_url(WP_REST_Request $request) {
    $postID = $request->get_param('postID');
    $type = $request->get_param('type') ?: 'dubbed';
    $video_sources = get_video_sources_by_post_id($postID);
    $typeMap = [
        'dubbed' => 'drivethais',
        'subbed' => 'drivesubs'
    ];
    $videoKey = $typeMap[$type] ?? null;
    if (empty($video_sources[$videoKey])) {
        return new WP_REST_Response('No video URL found for the specified type', 404);
    }
    return new WP_REST_Response($video_sources[$videoKey], 200);
}

function comment_support_for_my_custom_post_type() {
     add_post_type_support( 'post', 'comments' );
}
add_action( 'init', 'comment_support_for_my_custom_post_type' );

add_theme_support("post-thumbnails", array('movie'));
register_sidebar(array("name" => "Sidebar Right", "id" => "sidebarright", "before_widget" => "<div class=\"sidebar-right\">", "after_widget" => "</div>", "before_title" => "<div class=\"sidebar-header\"><h2>", "after_title" => "</h2></div>"));
register_sidebar(array("name" => "Sidebar Left", "id" => "sidebarleft", "before_widget" => "<div class=\"sidebar-left\">", "after_widget" => "</div>", "before_title" => "<div class=\"sidebar-header\"><h2>", "after_title" => "</h2></div>"));

function generate_tags($post_id) {
    list($english_title, $thai_title, $year) = split_title($post_id);
    $tags = array();
    $tags = array(
        "ดูหนัง $english_title",
        "$english_title Master",
        "$english_title",
        "$english_title HD",
        "$thai_title",
        "$thai_title HD"
    );

    $current_year = date("Y");
    if ($year == $current_year) {
        array_push($tags, "ดูหนังใหม่ $current_year");
    }
    return $tags;
}

function generate_and_set_tags($post_id) {
    wp_set_post_tags($post_id, null);
    $tags = generate_tags($post_id);
    wp_set_post_tags($post_id, $tags);
}
add_action('save_post', 'generate_and_set_tags');

function custom_pagination($query = null) {
    global $wp_query;
    if ($query) {
        $total = $query->max_num_pages;
    } else {
        $total = $wp_query->max_num_pages;
    }

    if ($total > 1) {
        $current_page = max(1, get_query_var('paged'));
        $pages = [];
        $pages[] = 1;

        for ($i = max(2, $current_page - 2); $i <= min($total - 1, $current_page + 2); $i++) {
            if (!in_array($i, $pages)) {
                $pages[] = $i;
            }
        }

        $prev_ten = floor($current_page / 10) * 10;
        $next_ten = ceil($current_page / 10) * 10;

        while (count($pages) < 9 && ($prev_ten > 1 || $next_ten < $total)) {
            if ($prev_ten > 1 && !in_array($prev_ten, $pages)) {
                array_unshift($pages, $prev_ten);
            }
            if ($next_ten < $total && !in_array($next_ten, $pages)) {
                $pages[] = $next_ten;
            }
            $prev_ten -= 10;
            $next_ten += 10;
        }

        if (!in_array($total, $pages)) {
            $pages[] = $total;
        }

        $pages = array_unique($pages);
        sort($pages);

        echo '<div class="pagination-x">';
        echo '<a class="prev page-numbers" href="' . get_pagenum_link(max(1, $current_page - 1)) . '">❮</a>';

        foreach ($pages as $page_number) {
            if ($page_number == $current_page) {
                echo '<span aria-current="page" class="page-numbers current">' . $page_number . '</span>';
            } else {
                echo '<a class="page-numbers" href="' . get_pagenum_link($page_number) . '">' . $page_number . '</a>';
            }
        }

        echo '<a class="next page-numbers" href="' . get_pagenum_link(min($total, $current_page + 1)) . '">❯</a>';
        echo '</div>';
    }
}

function custom_page_template_include($template) {
    if (is_page()) {
        global $post;
        $slug = get_post_field('post_name', $post->ID);
        $custom_template = locate_template('Content-Page/page-' . $slug . '.php');
        if ($custom_template) {
            return $custom_template;
        } else {
            return locate_template('Content-Page/page-default.php');
        }
    }
    return $template;
}
add_filter('template_include', 'custom_page_template_include');

function display_views_formatted($post_id) {
    $views_file = VIEW_COUNT_DIR . 'post_' . $post_id . '_enhanced_views.json';
    if (file_exists($views_file)) {
        $file_contents = file_get_contents($views_file);
        $views = is_numeric($file_contents) ? intval($file_contents) : 0;
    } else {
        $views = intval(get_post_meta($post_id, 'views', true));
    }
    if ($views < 10000) {
        echo number_format($views);
    } elseif ($views < 1000000) {
        echo number_format($views / 1000, 2) . 'K';
    } else {
        echo number_format($views / 1000000, 3) . 'M';
    }
}

if (!defined('VIEW_COUNT_DIR')) {
    $upload_dir = wp_upload_dir();
    define('VIEW_COUNT_DIR', trailingslashit($upload_dir['basedir']) . 'view_counts/');
    if (!file_exists(VIEW_COUNT_DIR)) {
        mkdir(VIEW_COUNT_DIR, 0755, true);
    }
}

function enhance_post_view_counts() {
    if (isset($_POST['post_id'])) {
        $post_id = intval($_POST['post_id']);
        if ($post_id > 0) {
            $views_key  = 'views';
            $views_file = VIEW_COUNT_DIR . 'post_' . $post_id . '_enhanced_views.json';
            if (!file_exists($views_file)) {
                $initial_views = (int) get_post_meta($post_id, $views_key, true);
                $fp = fopen($views_file, 'c+');
                if ($fp) {
                    if (flock($fp, LOCK_EX)) {
                        ftruncate($fp, 0);
                        fwrite($fp, $initial_views);
                        fflush($fp);
                        flock($fp, LOCK_UN);
                    } else {
                        custom_log("enhance_post_view_counts: ไม่สามารถล็อกไฟล์สำหรับเขียนได้: $views_file");
                    }
                    fclose($fp);
                } else {
                    custom_log("enhance_post_view_counts: ไม่สามารถเปิดไฟล์สำหรับเขียนได้: $views_file");
                }
            }
            $current_views = 0;
            $fp = fopen($views_file, 'r');
            if ($fp) {
                if (flock($fp, LOCK_SH)) {
                    $filesize = filesize($views_file);
                    $file_contents = $filesize > 0 ? fread($fp, $filesize) : '';
                    flock($fp, LOCK_UN);
                } else {
                    custom_log("enhance_post_view_counts: ไม่สามารถล็อกไฟล์สำหรับอ่านได้: $views_file");
                    $file_contents = '';
                }
                fclose($fp);
                $current_views = is_numeric($file_contents) ? (int) $file_contents : (int) get_post_meta($post_id, $views_key, true);
            } else {
                custom_log("enhance_post_view_counts: ไม่สามารถเปิดไฟล์สำหรับอ่านได้: $views_file");
                $current_views = (int) get_post_meta($post_id, $views_key, true);
            }
            if ($current_views < 10000) {
                $random_increment = rand(70, 99);
            } elseif ($current_views >= 10000 && $current_views < 50000) {
                $random_increment = rand(40, 69);
            } elseif ($current_views >= 50000 && $current_views < 100000) {
                $random_increment = rand(20, 39);
            } else {
                $random_increment = rand(3, 9);
            }
            $current_views += $random_increment;
            $fp = fopen($views_file, 'w');
            if ($fp) {
                if (flock($fp, LOCK_EX)) {
                    fwrite($fp, $current_views);
                    fflush($fp);
                    flock($fp, LOCK_UN);
                } else {
                    custom_log("enhance_post_view_counts: ไม่สามารถล็อกไฟล์สำหรับเขียนได้: $views_file");
                }
                fclose($fp);
            } else {
                custom_log("enhance_post_view_counts: ไม่สามารถเปิดไฟล์สำหรับเขียนได้: $views_file");
            }
            echo $current_views;
        }
    }
    wp_die();
}
add_action('wp_ajax_nopriv_enhance_views', 'enhance_post_view_counts');
add_action('wp_ajax_enhance_views', 'enhance_post_view_counts');

function sync_views_to_db() {
    $files = glob(VIEW_COUNT_DIR . 'post_*_enhanced_views.json');
    if ($files) {
        foreach ($files as $file) {
            if (preg_match('/post_(\d+)_enhanced_views\.json/', basename($file), $matches)) {
                $post_id = intval($matches[1]);
                $views = 0;
                $fp = fopen($file, 'r');
                if ($fp) {
                    if (flock($fp, LOCK_SH)) {
                        $filesize = filesize($file);
                        $file_contents = $filesize > 0 ? fread($fp, $filesize) : '';
                        flock($fp, LOCK_UN);
                    } else {
                        custom_log("sync_views_to_db: ไม่สามารถล็อกไฟล์สำหรับอ่านได้: $file");
                        $file_contents = '';
                    }
                    fclose($fp);
                    $views = is_numeric($file_contents) ? (int) $file_contents : 0;
                    update_post_meta($post_id, 'views', $views);
                } else {
                    custom_log("sync_views_to_db: ไม่สามารถเปิดไฟล์สำหรับอ่านได้: $file");
                }
            }
        }
    }
}
if (!wp_next_scheduled('daily_views_sync')) {
    $timezone = new DateTimeZone('Asia/Bangkok');
    $now = new DateTime('now', $timezone);
    $next_run = new DateTime('today 06:00:00', $timezone);
    if ($now > $next_run) {
        $next_run->modify('+1 day');
    }
    wp_schedule_event($next_run->getTimestamp(), 'daily', 'daily_views_sync');
}
add_action('daily_views_sync', 'sync_views_to_db');

function insert_post_view_count_script() {
    if (is_single()) {
        echo "<script>
        document.addEventListener('DOMContentLoaded', () => {
            var postId = document.body.dataset.postId;
            if (postId) {
                fetch('" . admin_url('admin-ajax.php') . "', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded;'},
                    body: 'action=enhance_views&post_id=' + postId
                }).then(response => {
                    if (response.ok) console.log('Views enhanced successfully.');
                });
            }
        });
        </script>";
    }
}
add_action('wp_head', 'insert_post_view_count_script');

function extract_domain_name() {
    $url = get_site_url();
    $host = parse_url($url, PHP_URL_HOST);
    if (!$host) return "";
    $parts = explode('.', $host);
    $count = count($parts);
    if ($count == 2) {
        $domain = $parts[0];
    } elseif ($count > 2) {
        if (strlen($parts[$count - 1]) == 2) {
            $domain = $parts[$count - 3];
        } else {
            $domain = $parts[$count - 2];
        }
    } else {
        $domain = $host;
    }
    return strtoupper($domain);
}

function custom_the_title($max_length = 40) {
    $title = get_the_title();
    if (mb_strlen($title, 'UTF-8') > $max_length) {
        $title = mb_substr($title, 0, $max_length, 'UTF-8');
        $last_space = mb_strrpos($title, ' ', 0, 'UTF-8');
        if ($last_space !== false) {
            $title = mb_substr($title, 0, $last_space, 'UTF-8');
        }
        $title .= '...';
    }
    echo esc_html($title);
}

function display_tags_and_categories($post_id) {
    $categories = get_the_category($post_id);
    $terms = $categories ? $categories : [];
    shuffle($terms);
    $terms = array_slice($terms, 0, 8);
    $additional_terms = [
        'ดูหนัง' => home_url('/'),
        'ดูหนังออนไลน์' => home_url('/'),
        'ดูหนังฟรี' => home_url('/'),
        'ดูหนังใหม่' => home_url('/')
    ];
    foreach ($additional_terms as $name => $link) {
        if (count($terms) < 8) {
            $terms[] = (object) ['name' => $name, 'link' => $link];
        } else {
            break;
        }
    }
    echo '<div class="movie-tags">';
    foreach ($terms as $term) {
        $link = isset($term->link) ? $term->link : get_term_link($term);
        echo '<a href="' . $link . '">' . $term->name . '</a> ';
    }
    echo '</div>';
}

function get_related_movies($post_id, $number_of_posts = 8) {
    $ptype = get_post_type($post_id);
    $tax   = $ptype === 'serie' ? 'serie_category' : ($ptype === 'anime' ? 'anime_category' : 'category');
    $terms = wp_get_post_terms($post_id, $tax, ['fields' => 'ids']);
    $related = [];
    
    if ($terms) {
        $q1 = new WP_Query([
            'post_type'      => $ptype,
            'posts_per_page' => $number_of_posts,
            'post__not_in'   => [$post_id],
            'tax_query'      => [[
                'taxonomy' => $tax,
                'field'    => 'term_id',
                'terms'    => $terms,
            ]],
            'orderby'        => 'rand'
        ]);
        
        while ($q1->have_posts()) { 
            $q1->the_post(); 
            $related[] = get_the_ID(); 
        }
        wp_reset_postdata();
    }

    $remaining = $number_of_posts - count($related);
    if ($remaining > 0) {
        $q2 = new WP_Query([
            'post_type'      => $ptype,
            'posts_per_page' => $remaining,
            'post__not_in'   => array_merge([$post_id], $related),
            'orderby'        => 'rand'
        ]);
        
        while ($q2->have_posts()) { 
            $q2->the_post(); 
            $related[] = get_the_ID(); 
        }
        wp_reset_postdata();
    }
    
    return $related;
}

function deemovie_add_custom_meta_boxes() {
    add_meta_box(
        'deemovie_movie_meta',
        'Movie Information',
        'deemovie_render_movie_meta_box',
        'movie',
        'normal',
        'high'
    );

    add_meta_box(
        'deemovie_serie_meta',
        'Movie Information',
        'deemovie_render_serie_meta_box',
        'serie',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'deemovie_add_custom_meta_boxes');

function deemovie_render_movie_meta_box($post) {
    $trailer = get_post_meta($post->ID, 'trailer', true);
    $featured_image_url = get_post_meta($post->ID, 'featured_image_url', true);
    $gdrivethai = get_post_meta($post->ID, 'gdrivethai', true);
    $gdrivesub = get_post_meta($post->ID, 'gdrivesub', true);

    echo '<p><label>Trailer (YouTube ID):</label><br />
        <input type="text" name="trailer" value="' . esc_attr($trailer) . '" class="widefat" /></p>';

    echo '<p><label>Featured Image URL:</label><br />
        <input type="text" name="featured_image_url" value="' . esc_attr($featured_image_url) . '" class="widefat" /></p>';

    echo '<p><label>Google Drive พากย์ไทย:</label><br />
        <input type="text" name="gdrivethai" value="' . esc_attr($gdrivethai) . '" class="widefat" /></p>';

    echo '<p><label>Google Drive ซับไทย:</label><br />
        <input type="text" name="gdrivesub" value="' . esc_attr($gdrivesub) . '" class="widefat" /></p>';
}

function deemovie_render_serie_meta_box($post) {
    $trailer = get_post_meta($post->ID, 'trailer', true);
    $featured_image_url = get_post_meta($post->ID, 'featured_image_url', true);
    $episodes_dub = get_post_meta($post->ID, 'serie_dubbed', true);
    $episodes_sub = get_post_meta($post->ID, 'serie_subbed', true);

    if (!is_array($episodes_dub)) $episodes_dub = [];
    if (!is_array($episodes_sub)) $episodes_sub = [];

    echo '<p><label>Trailer (YouTube ID):</label><br />
        <input type="text" name="trailer" value="' . esc_attr($trailer) . '" class="widefat" /></p>';

    echo '<p><label>Featured Image URL:</label><br />
        <input type="text" name="featured_image_url" value="' . esc_attr($featured_image_url) . '" class="widefat" /></p>';

    echo '<hr><h3>Episode พากย์ไทย</h3>';
    echo '<div id="episodes_dub_container">';
    foreach ($episodes_dub as $index => $link) {
        echo '<div style="margin-bottom:10px; display: flex; gap: 10px; align-items: center; background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                <strong>Episode ' . ($index + 1) . ':</strong>
                <input type="text" name="episodes_dub[]" value="' . esc_attr($link) . '" style="flex: 1;" />
                <button type="button" class="button remove-episode">Remove</button>
              </div>';
    }
    echo '</div><br /><button type="button" class="button button-primary add-episode" data-target="episodes_dub_container" style="background-color: #4CAF50; border-color: #4CAF50; width: 100%; font-weight: bold;">Add Episode</button>';

    echo '<hr><h3>Episode ซับไทย</h3>';
    echo '<div id="episodes_sub_container">';
    foreach ($episodes_sub as $index => $link) {
        echo '<div style="margin-bottom:10px; display: flex; gap: 10px; align-items: center; background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                <strong>Episode ' . ($index + 1) . ':</strong>
                <input type="text" name="episodes_sub[]" value="' . esc_attr($link) . '" style="flex: 1;" />
                <button type="button" class="button remove-episode">Remove</button>
              </div>';
    }
    echo '</div><br /><button type="button" class="button button-primary add-episode" data-target="episodes_sub_container" style="background-color: #4CAF50; border-color: #4CAF50; width: 100%; font-weight: bold;">Add Episode</button>';

    echo '<script>
    document.addEventListener("DOMContentLoaded", function() {
        function updateEpisodeNumbers(containerId) {
            const container = document.getElementById(containerId);
            const items = container.querySelectorAll("div");
            items.forEach((item, index) => {
                const label = item.querySelector("strong");
                if (label) label.textContent = `Episode ${index + 1}:`;
            });
        }

        document.querySelectorAll(".add-episode").forEach(function(btn) {
            btn.addEventListener("click", function() {
                const targetId = this.getAttribute("data-target");
                const container = document.getElementById(targetId);
                const wrapper = document.createElement("div");
                wrapper.style.marginBottom = "10px";
                wrapper.style.display = "flex";
                wrapper.style.gap = "10px";
                wrapper.style.alignItems = "center";
                wrapper.style.background = "#fff";
                wrapper.style.padding = "10px";
                wrapper.style.border = "1px solid #ddd";
                wrapper.style.borderRadius = "4px";
                const count = container.children.length + 1;
                wrapper.innerHTML = `<strong>Episode ${count}:</strong> <input type="text" name="${targetId === "episodes_dub_container" ? "episodes_dub[]" : "episodes_sub[]"}" style="flex: 1;" /> <button type="button" class="button remove-episode">Remove</button>`;
                container.appendChild(wrapper);
                updateEpisodeNumbers(targetId);
            });
        });

        document.addEventListener("click", function(e) {
            if (e.target.classList.contains("remove-episode")) {
                const container = e.target.closest("div").parentNode;
                e.target.closest("div").remove();
                updateEpisodeNumbers(container.id);
            }
        });
    });
    </script>';
}

function deemovie_save_custom_meta($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;

    $post_type = get_post_type($post_id);

    $shared_fields = ['trailer', 'featured_image_url'];
    foreach ($shared_fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }

    if ($post_type === 'movie') {
        $movie_fields = ['gdrivethai', 'gdrivesub'];
        foreach ($movie_fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
            }
        }
    }

    if ($post_type === 'serie') {
        if (isset($_POST['serie_dubbed']) && is_array($_POST['serie_dubbed'])) {
            update_post_meta($post_id, 'serie_dubbed', array_filter(array_map('sanitize_text_field', $_POST['serie_dubbed'])));
        }

        if (isset($_POST['serie_subbed']) && is_array($_POST['serie_subbed'])) {
            update_post_meta($post_id, 'serie_subbed', array_filter(array_map('sanitize_text_field', $_POST['serie_subbed'])));
        }
    }
}
add_action('save_post', 'deemovie_save_custom_meta');

