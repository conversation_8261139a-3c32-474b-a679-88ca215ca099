<?php
/**
 * Movie Category Slider Component
 * แสดงหนังแบบสไลด์ตามหมวดหมู่
 */

// ดึงหมวดหมู่ทั้งหมด
function get_movie_categories() {
    $categories = get_terms(array(
        'taxonomy' => 'category',
        'hide_empty' => true,
        'exclude' => array(1), // ไม่รวม Uncategorized
        'number' => 10 // จำกัด 10 หมวดหมู่
    ));
    
    return $categories;
}

// ดึงหนังตามหมวดหมู่
function get_movies_by_category($category_slug, $limit = 12) {
    $args = array(
        'post_type' => 'movie',
        'posts_per_page' => $limit,
        'category_name' => $category_slug,
        'post_status' => 'publish',
        'meta_key' => 'views',
        'orderby' => 'meta_value_num',
        'order' => 'DESC'
    );
    
    return new WP_Query($args);
}

// AJAX Handler สำหรับโหลดหนังตามหมวดหมู่
add_action('wp_ajax_load_category_movies', 'load_category_movies_ajax');
add_action('wp_ajax_nopriv_load_category_movies', 'load_category_movies_ajax');

function load_category_movies_ajax() {
    $category_slug = sanitize_text_field($_POST['category_slug']);
    $movies_query = get_movies_by_category($category_slug);
    
    if ($movies_query->have_posts()) {
        ob_start();
        while ($movies_query->have_posts()) : $movies_query->the_post();
            ?>
            <div class="movie-slide-item">
                <a href="<?php the_permalink(); ?>" class="movie-link">
                    <div class="movie-poster">
                        <?php if (has_post_thumbnail()) : ?>
                            <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'medium'); ?>" 
                                 alt="<?php the_title_attribute(); ?>" 
                                 loading="lazy">
                        <?php endif; ?>
                        
                        <div class="movie-overlay">
                            <div class="movie-rating">
                                <i class="star-icon">★</i>
                                <span><?php display_imdb_rating(); ?></span>
                            </div>
                            <div class="movie-quality">HD</div>
                        </div>
                    </div>
                    
                    <div class="movie-info">
                        <h3 class="movie-title"><?php custom_the_title(); ?></h3>
                        <div class="movie-meta">
                            <span class="movie-year"><?php echo get_movie_year(get_the_ID()); ?></span>
                            <span class="movie-audio"><?php echo get_audio_type(get_the_ID()); ?></span>
                        </div>
                    </div>
                </a>
            </div>
            <?php
        endwhile;
        wp_reset_postdata();
        
        $output = ob_get_clean();
        wp_send_json_success($output);
    } else {
        wp_send_json_error('ไม่พบหนังในหมวดหมู่นี้');
    }
}

// ฟังก์ชันแสดง Movie Slider
function display_movie_slider() {
    $categories = get_movie_categories();
    if (empty($categories)) return;
    
    // หมวดหมู่แรกเป็นค่าเริ่มต้น
    $first_category = $categories[0];
    $initial_movies = get_movies_by_category($first_category->slug);
    ?>
    
    <div class="movie-slider-container">
        <!-- Category Tabs -->
        <div class="category-tabs">
            <div class="tabs-wrapper">
                <?php foreach ($categories as $index => $category) : ?>
                    <button class="category-tab <?php echo $index === 0 ? 'active' : ''; ?>" 
                            data-category="<?php echo esc_attr($category->slug); ?>">
                        <?php echo esc_html($category->name); ?>
                        <span class="tab-count">(<?php echo $category->count; ?>)</span>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Movie Slider -->
        <div class="movie-slider-wrapper">
            <button class="slider-arrow slider-prev" aria-label="Previous">
                <i class="arrow-icon">‹</i>
            </button>
            
            <div class="movie-slider">
                <div class="movie-slides" id="movie-slides">
                    <?php if ($initial_movies->have_posts()) : ?>
                        <?php while ($initial_movies->have_posts()) : $initial_movies->the_post(); ?>
                            <div class="movie-slide-item">
                                <a href="<?php the_permalink(); ?>" class="movie-link">
                                    <div class="movie-poster">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'medium'); ?>" 
                                                 alt="<?php the_title_attribute(); ?>" 
                                                 loading="lazy">
                                        <?php endif; ?>
                                        
                                        <div class="movie-overlay">
                                            <div class="movie-rating">
                                                <i class="star-icon">★</i>
                                                <span><?php display_imdb_rating(); ?></span>
                                            </div>
                                            <div class="movie-quality">HD</div>
                                        </div>
                                    </div>
                                    
                                    <div class="movie-info">
                                        <h3 class="movie-title"><?php custom_the_title(); ?></h3>
                                        <div class="movie-meta">
                                            <span class="movie-year"><?php echo get_movie_year(get_the_ID()); ?></span>
                                            <span class="movie-audio"><?php echo get_audio_type(get_the_ID()); ?></span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endwhile; ?>
                        <?php wp_reset_postdata(); ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <button class="slider-arrow slider-next" aria-label="Next">
                <i class="arrow-icon">›</i>
            </button>
        </div>
        
        <!-- Loading Indicator -->
        <div class="slider-loading" id="slider-loading" style="display: none;">
            <div class="loading-spinner"></div>
            <span>กำลังโหลด...</span>
        </div>
    </div>
    
    <?php
}

// Helper function สำหรับดึงปีของหนัง
function get_movie_year($post_id) {
    $title_parts = split_title($post_id);
    return isset($title_parts[2]) ? $title_parts[2] : date('Y');
}
?>
