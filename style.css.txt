/*
 * WordPress Theme Style - Complete with Responsive Fix
 * แก้ไขปัญหาการแสดงผลตัวอักษรขยายตัวในมือถือ
 */

/* ===== BASE STYLES ===== */
body {
    background: #0a0a0a;
    margin: 0;
    font-family: 'Kanit', sans-serif;
    font-weight: 300;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    /* ป้องกันการปรับขนาดข้อความอัตโนมัติ */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

*,*:before,*:after {
    box-sizing: inherit;
    /* ป้องกันการปรับขนาดข้อความอัตโนมัติ */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

html {
    box-sizing: border-box;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

a {
    color: #d4d4d4;
    text-decoration: none;
}

body,button,input,select,optgroup,textarea {
    color: #d4d4d4;
    font-family: 'Kanit', sans-serif;
    font-weight: 300;
    font-size: 1rem;
    line-height: 1.5;
}

h1,h2,h3,h4,h5,h6 {
    clear: both;
    font-family: 'Kanit', sans-serif;
    font-weight: 700;
}

main {
    display: block;
}

/* ===== RESPONSIVE CONTAINER SYSTEM ===== */
/* Desktop First - แล้วปรับลงมา */
.site-header {
    width: 100%;
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
}

.home .site-header {
    width: 100%;
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
}

/* Container responsive */
#page {
    min-width: 1220px; /* Desktop */
    width: 100%;
}

.home #page {
    min-width: 1400px; /* Home Desktop */
    width: 100%;
}

/* ===== ENHANCED HEADER ===== */
.master-head {
    position: relative;
    background: #000000;
    overflow: hidden;
    border-bottom: 1px solid rgba(255, 107, 157, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
}

/* Animated Geometric Background */
.master-head::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 25%, rgba(255, 107, 157, 0.12) 0%, transparent 40%),
        radial-gradient(circle at 85% 75%, rgba(116, 185, 255, 0.10) 0%, transparent 40%),
        radial-gradient(circle at 50% 10%, rgba(196, 69, 105, 0.08) 0%, transparent 30%);
    animation: floatingGraphics 20s ease-in-out infinite;
    z-index: 1;
}

.master-head::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(45deg, transparent 30%, rgba(255, 107, 157, 0.03) 32%, transparent 34%),
        linear-gradient(-45deg, transparent 30%, rgba(116, 185, 255, 0.03) 32%, transparent 34%),
        linear-gradient(90deg, transparent 48%, rgba(196, 69, 105, 0.02) 50%, transparent 52%);
    background-size: 80px 80px, 100px 100px, 120px 120px;
    animation: geometricPattern 25s linear infinite;
    z-index: 2;
}

/* ===== SITE BRANDING ===== */
.site-branding {
    position: relative;
    z-index: 10;
    width: 100%;
}

.site-branding .header-description h1 {
    text-align: center;
    color: #999;
    font-size: 25px;
    font-family: 'Kanit', sans-serif;
    font-weight: 600;
    background: linear-gradient(45deg, #ff6b9d, #74b9ff, #ff6b9d);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 4s ease-in-out infinite;
    text-shadow: 0 0 15px rgba(255, 107, 157, 0.2);
    margin-bottom: 15px;
}

.site-branding ul {
    margin: 0;
    padding: 0;
    text-align: center;
}

/* ===== HEADER INFO ===== */
.header-info {
    width: 1440px; /* Desktop */
    margin: 0 auto;
    display: grid;
    grid-template-columns: 15% 70% 15%;
    align-items: center;
    padding: 10px 0 25px;
    position: relative;
    z-index: 10;
}

.container-index {
    width: 1440px; /* Desktop */
    margin: 0 auto;
}

/* ===== HEADER LOGO ===== */
section.header-logo {
    position: relative;
    z-index: 10;
}

section.header-logo img {
    width: 220px;
    object-fit: cover;
    float: right;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 2px 8px rgba(255, 107, 157, 0.3));
    border-radius: 8px;
}

section.header-logo img:hover {
    transform: scale(1.05) rotate(1deg);
    filter: drop-shadow(0 4px 12px rgba(255, 107, 157, 0.5));
}

/* ===== SEARCH INPUT ===== */
.search-input {
    position: relative;
    z-index: 10;
}

#searchform {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

input#input-ser {
    padding: 7px 20px;
    border-radius: 25px;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(116, 185, 255, 0.1));
    backdrop-filter: blur(15px);
    color: white;
    border: 1px solid rgba(255, 107, 157, 0.3);
    margin: 0;
    background-image: url(http://mv-tv.com/wp-content/uploads/2024/09/icon-search.png);
    background-repeat: no-repeat;
    background-position: 16px center;
    background-size: 16px;
    text-indent: 16px;
    width: 75%;
    font-size: 14px;
    outline: 0;
    font-family: 'Kanit', sans-serif;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

input#input-ser::placeholder {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 300;
}

input#input-ser:focus {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.15), rgba(116, 185, 255, 0.15));
    border-color: rgba(116, 185, 255, 0.6);
    box-shadow:
        0 8px 25px rgba(116, 185, 255, 0.4),
        0 0 20px rgba(255, 107, 157, 0.3);
    transform: translateY(-1px) scale(1.02);
}

input#input-ser:hover {
    border-color: rgba(255, 107, 157, 0.5);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.2);
}

/* ===== CONTACT BUTTON ===== */
.contact .hd-contact a {
    background: linear-gradient(148deg, #ff6b9d, #c44569, #74b9ff);
    padding: 10px 25px;
    font-size: 20px;
    line-height: 1;
    font-family: 'Kanit', sans-serif;
    font-weight: 300;
    border-radius: 27px;
    display: inline-flex;
    align-items: center;
    width: 180px;
    height: 50px;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 10;
    color: #fff;
}

.contact .hd-contact a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.contact .hd-contact a:hover::before {
    left: 100%;
}

.contact .hd-contact a:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 8px 25px rgba(116, 185, 255, 0.4),
        0 0 20px rgba(255, 107, 157, 0.3);
    color: white;
}

/* ===== MAIN NAVIGATION ===== */
.main-navigation {
    display: block;
    width: 100%;
    backdrop-filter: blur(15px);
    position: relative;
    z-index: 10;
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
}

.main-navigation ul {
    display: block;
    list-style: none;
    margin: 0;
    padding-left: 0;
    text-align: center;
}

.menu-menu-container {
    width: 100%;
    text-align: center;
}

.main-navigation li {
    position: relative;
    display: inline-flex;
    padding: 0 2%;
}

.main-navigation a {
    display: block;
    text-decoration: none;
    padding: 20px 0;
    font-family: 'Kanit', sans-serif;
    font-weight: 600;
    color: #999;
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-radius: 15px;
    margin: 5px;
    font-size: 18px;
}

.main-navigation a:before {
    content: "";
    position: absolute;
    height: 5px;
    left: 20%;
    right: 20%;
    bottom: 12px;
    border-radius: 15px;
    background: linear-gradient(148deg, #ff6b9d, #74b9ff, #c44569);
    visibility: hidden;
    transition: all 0.3s ease;
}

.main-navigation a:hover,
.main-navigation .current-menu-item a {
    color: #fff;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.15), rgba(116, 185, 255, 0.1));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
}

.main-navigation a:hover:before,
.main-navigation .current-menu-item a:before {
    visibility: visible;
}

/* ===== MAIN CONTENT ===== */
#main {
    overflow: hidden;
    border-radius: 5px;
    margin-bottom: 30px;
}

#main h2 {
    display: block;
    border-radius: 10px;
    background: linear-gradient(148deg, #f770e0, #c121f3, #25b4f5);
    padding: 7px 0;
    margin: 0 0 10px;
    text-align: center;
    font-size: 20px;
    color: #fff;
    font-family: 'Kanit', sans-serif;
    font-weight: 300;
}

.grid-movie-index {
    display: grid;
    grid-template-columns: repeat(5, 193.2px); /* Desktop */
    grid-gap: 10px;
    margin-bottom: 15px;
}

.movie_box {
    overflow: hidden;
    position: relative;
    margin-bottom: 15px;
}

.movie_box a {
    background: #3b3d45;
    text-decoration: none;
}

.movie_box p {
    font-size: 14px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    text-align: center;
    color: #fff;
    margin: 10px 5px;
    height: 40px;
}

span.movie-lang {
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1;
    color: #999;
    background: #000;
    margin: 0 auto;
    padding: 5px 15px;
    border-radius: 15px;
    text-align: center;
    display: table;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

span.movie-lang:hover {
    color: #fff !important;
    background: #00c6ff !important;
    cursor: pointer;
}

.figure-box span.box-movie-star {
    background: url(http://mv-tv.com/wp-content/uploads/2024/09/star-mvtv.png) no-repeat left;
    background-position: 8px;
    background-position-y: 5px;
    position: absolute;
    top: 5px;
    right: 5px;
    color: #f2b000;
    font-size: 14px;
    line-height: 1;
    border-radius: 10px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    background-color: #000000cc;
    padding: 5px 10px 5px 25px;
}

.figure-box span.box-movie-hd {
    position: absolute;
    top: 5px;
    left: 5px;
    border-radius: 10px;
    background: #000000cc;
    padding: 5px 10px;
    font-size: 14px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    line-height: 1;
    color: #ad6ff3;
}

/* ===== MOVIE VIEWS COUNTER ===== */
.figure-box span.box-movie-views {
    position: absolute;
    top: 71%;
    left: 80%;
    transform: translate(-50%, -50%);
    border-radius: 25px;
    background: rgb(0 0 0 / 31%);
    backdrop-filter: blur(15px);
    padding: 8px 15px 8px 35px;
    font-size: 14px;
    font-family: 'Kanit', sans-serif;
   font-weight: 500;
    line-height: 1;
    color: #00c6ff;
   background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="%2300c6ff"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>');
    background-repeat: no-repeat;
     background-position: 12px center;
    background-size: 16px;
    transition: all 0.3s ease;
     background-position: 12px center;
    background-size: 16px;
    transition: all 0.3s ease;
}

.figure-box span.box-movie-views:hover {
     background: rgba(0, 198, 255, 0.3);
    color: #fff;
    transform: scale(1.05);
    border-color: rgba(0, 198, 255, 0.6);
	transform: translate(-50%, -50%) scale(1.1);
    border-color: rgba(0, 198, 255, 0.8);
    box-shadow: 0 6px 20px rgba(0, 198, 255, 0.4);
    cursor: pointer;
    opacity: 1;
}



/* Responsive views counter */
@media (max-width: 767px) {
    .figure-box span.box-movie-views {
        font-size: 12px;
        padding: 6px 12px 6px 28px;
        background-size: 14px;
        background-position: 10px center;
        border-radius: 20px;
    }

    .figure-box span.box-movie-views.style-2 {
        padding: 3px 6px 3px 18px;
        background-position: 4px center;
    }

    .figure-box span.box-movie-views.style-3 {
        font-size: 10px;
        padding: 2px 6px;
    }
}

@media (max-width: 480px) {
    .figure-box span.box-movie-views {
       font-size: 11px;
        padding: 5px 10px 5px 24px;
        background-size: 12px;
        background-position: 8px center;
        border-radius: 18px;
    }
}

.movie_box:hover span.movie-lang {
    color: #66ccff;
    background: #292627;
}

.movie_box a img {
    vertical-align: middle;
    width: 100%;
    object-fit: cover;
}

.movie_box img {
    border-radius: 10px;
    border: 3px solid #4a4a4a;
}

.movie_box img:hover {
    border: 3px solid #66ccff;
}

.grid-movie-index .movie_box img {
    height: unset;
}

.grid-main {
    display: grid;
    grid-template-columns: 204px 1006px 204px; /* Desktop */
    grid-gap: 13px;
    margin-top: 20px;
    width: 1440px; /* Desktop */
    margin: 10px auto;
}

/* ===== CONTENT SECTIONS ===== */
.nds-title {
    margin: 15px 0 20px;
    border-bottom: 2px solid #303030;
    padding-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nds-title h2, .nds-title h3 {
    margin: 0;
    color: #fff;
    font-size: 24px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    line-height: 1;
}

.grid-movie-advise {
    display: grid;
    grid-template-columns: repeat(8, 171.25px); /* Desktop */
    grid-gap: 10px;
    margin-bottom: 15px;
}

.grid-movie-advise img {
    height: 250px;
}

.mp4hd-content p {
    font-size: 14px;
    padding: 20px 40px;
    margin: 45px 0 50px;
    background: #181818;
    border-radius: 10px;
    color: #fff;
}

/* ===== SIDEBAR ===== */
.sidebar-header h2 {
    background: linear-gradient(148deg, #f770e0, #c121f3, #25b4f5);
    padding: 12.5px 0;
    margin: 0;
    text-align: center;
    font-size: 18px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    line-height: 1;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom: 1px solid #424040;
    color:#fff;
}

.custom-menu-widget {
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    background: #181818;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    list-style: none !important;
    text-align: center;
    line-height: 35px;
    font-family: 'Kanit', sans-serif;
    font-weight: 300;
}

.sidebar ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 0 !important;
    line-height: 1 !important;
    padding-right: 15px;
    font-size: 14px !important;
    margin: 0px;
    position: relative;
    list-style-type: none;
}

.sidebar li:hover {
    background: linear-gradient(148deg, #00c6ff, #008be5, #0056b3);
    font-weight: 700;
    color: #000000;
}

.sidebar li:hover a {
    color: #000000 !important;
    text-decoration: none;
}

.sidebar ul li a {
    display: inline-block;
    color: #999 !important;
    transition: all .5s ease;
    -webkit-transition: all .5s ease;
    -ms-transition: all .5s ease;
    -moz-transition: all .5s ease;
    font-size: 14px !important;
    float: left !important;
    font-weight: 400;
    padding: 15px 0 15px 20px;
    font-family: 'Kanit', sans-serif;
    text-decoration: none;
}

.sidebar {
    margin-bottom: 10px;
    overflow: hidden;
}

/* ===== PAGINATION ===== */
.pagination-x {
    overflow: hidden;
    text-align: center;
    margin: 60px 0;
}

span.page-numbers.current {
    background: linear-gradient(148deg, #00c6ff, #008be5, #0056b3);
    display: inline-flex;
    padding: 6px 16px;
    margin: 0 5px;
    color: #1c1b1b;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    border-radius: 20px;
}

a.page-numbers,span.page-numbers.dots {
    background: #212120;
    display: inline-flex;
    padding: 6px 16px;
    margin: 0 5px;
    border-radius: 20px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    color: #fff;
}

a.page-numbers:hover {
    color: #00c6ff;
}

/* ===== SINGLE POST ===== */
.h1-text h1, .h1-text h2, .h1-text h4 {
    display: block;
    border-radius: 10px;
    background: #181818;
    padding: 7px 0;
    margin: 0 0 10px;
    text-align: center;
    font-size: 20px;
    color: #fff;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
}

#main-movie {
    overflow: hidden;
    border-radius: 5px;
    margin-bottom: 30px;
}

.title-box-movie h1 {
    display: block;
    border-radius: 10px;
    background: linear-gradient(148deg, #f770e0, #c121f3, #25b4f5);
    padding: 7px 0;
    margin: 0 0 10px;
    text-align: center;
    font-size: 20px;
    color: #fff;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
}

.informs-movie {
    max-width: 100%;
    display: grid;
    grid-template-columns: 100%;
    row-gap: 25px;
}

.trailer-movie {
    position: relative;
    padding-bottom: 56.23%;
    height: 0;
    overflow: hidden;
    max-width: 100%;
    display: block;
}

.trailer-movie img {
    object-fit: cover;
}

.trailer-movie iframe {
    width: 100% !important;
    height: 100% !important;
    float: left;
    display: block;
    position: absolute;
}

/* ===== MOVIE DETAILS ===== */
.h2-trailer h2 {
    font-size: 16px !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 400 !important;
    text-align: center !important;
    margin: -15px 0 -10px !important;
}

.info-blog .detail-movie {
    background: #212120;
    color: #eee;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 17px;
}

.info-blog .detail-movie span {
    font-size: 14px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
}

span.movies-lang i,.movie-time i {
    margin-right: 5px;
}

.info-blog .detail-movie span.star {
    background: #0d0d0c;
    padding: 1px 10px 0 0;
    border-radius: 5px;
    color: #f5c518;
    font-size: 16px;
}

span.movie-hd {
    background: #0d0d0c;
    color: #f0f;
    padding: 5px 13px;
    border-radius: 17px;
}

span.movie-zoom {
    background: #0d0d0c;
    color: #fac346;
    padding: 5px 13px;
    border-radius: 17px;
}

span.movie-4k {
    color: #0ff;
    background: #0d0d0c;
    padding: 5px 13px;
    border-radius: 17px;
}

.info-blog .detail-movie img {
    margin-bottom: -5px;
    margin-right: 5px;
    object-fit: cover;
}

.blog1 a {
    color: #eee;
}

.movie-description .thumb-img img {
    border-radius: 10px;
    object-fit: cover;
}

.movie-description {
    margin-bottom: 15px;
    display: grid;
    grid-template-columns: 270px 1fr; /* Desktop */
    grid-gap: 10px;
}

.movie-excerpt {
    padding: 10px 15px;
    position: relative;
    height: 100%;
    overflow: hidden;
}

.movie-excerpt.temp-ep {
    padding: 20px 15px;
    border-radius: 5px;
    background: #212120;
}

.movie-excerpt h4 {
    color: #fff;
    font-size: 20px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    margin: 0;
    margin-bottom: 10px;
}

.movie-excerpt p {
    color: #d4d4d4;
    font-size: 14px;
    margin: 15px 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 12;
    overflow: hidden;
    text-overflow: ellipsis;
    height:255px;
    font-family: 'Kanit', sans-serif;
    font-weight: 300;
}

.movie-excerpt a {
    color: #f7b40d;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
}

.movie-excerpt a:hover {
    text-decoration: underline;
}

.movie-tags a {
    display: inline-flex;
    margin: 0 5px 5px 0;
    padding: 3px 20px;
    background: #212120;
    border-radius: 20px;
    font-size: 14px;
    font-family: 'Kanit', sans-serif;
    font-weight: 400;
    color: #999;
    text-decoration: none !important;
    width: 166px;
    justify-content: center;
}

.movie-tags a:hover {
    color: #000 !important;
    background: linear-gradient(148deg, #00c6ff, #008be5, #0056b3);
}

.movie-tags {
    position: absolute;
    float: left;
    display: block;
    height: 64px;
    bottom: 10px;
}

.playing-movie {
    margin: 15px 0 25px 0;
    position: relative;
}

iframe.res-iframe {
    width: 100%;
    height: 558px;
    display: block;
}

ul.custom-menu-widget.movie li a:hover {
    color: #00c6ff !important;
}

.main-left ul li a:hover {
    color: #00c6ff !important;
}

.report-button-wrapper {
    margin-bottom: 30px;
    background: linear-gradient(90deg, #ff6fd8 0%, #38a3d1 100%);
    padding: 0px 0;
    border-radius: 24px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
}

.report-button-wrapper a:hover{
    transform: translateY(-3px) scale(1.04);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.20);
    background: linear-gradient(90deg, #ff8ae2 0%, #8fd3f4 100%);
}

.report-button-wrapper a {
    display: inline-block !important;
    padding: 14px 36px;
    color: #fff;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.2rem;
    border: none;
    border-radius: 32px;
    box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.10);
    transition: transform 0.2s, box-shadow 0.2s;
    letter-spacing: 1px;
}

/* ===== ANIMATIONS ===== */
@keyframes floatingGraphics {
    0%, 100% {
        opacity: 1;
        transform: translateY(0px) scale(1);
    }
    25% {
        opacity: 0.8;
        transform: translateY(-10px) scale(1.02);
    }
    50% {
        opacity: 0.6;
        transform: translateY(-5px) scale(0.98);
    }
    75% {
        opacity: 0.9;
        transform: translateY(-15px) scale(1.01);
    }
}

@keyframes geometricPattern {
    0% {
        background-position: 0px 0px, 0px 0px, 0px 0px;
    }
    100% {
        background-position: 80px 80px, -100px 100px, 120px -120px;
    }
}

@keyframes titleGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes searchPulse {
    0%, 100% { opacity: 0.8; transform: translateY(-50%) scale(1); }
    50% { opacity: 1; transform: translateY(-50%) scale(1.05); }
}

/* ===== FOOTER STYLES - OPTIMIZED VERSION ===== */
:root {
  --footer-bg-image: url(http://mv-tv.com/wp-content/uploads/2024/09/inside-out-2-5422x2160-17820-scaled.jpg);
  --footer-primary: #00c6ff;
  --footer-secondary: #008be5;
  --footer-tertiary: #0056b3;
  --footer-success: #28af64;
  --footer-text-light: #c7c7c7;
  --footer-text-muted: #989799;
  --footer-text-dark: #999;
  --footer-bg-dark: #212120;
  --footer-border: #2a2936;
  --footer-border-light: #303030;
  --footer-white: #fff;
  --footer-black: #000;
  --footer-max-width: 1440px;
  --footer-transition: all 0.3s ease;
}

/* ลบ Animation ที่ทำให้ค้าง - เก็บแค่ที่จำเป็น */
@keyframes simpleHover {
  from { opacity: 0.8; }
  to { opacity: 1; }
}

.site-footer {
  position: relative;
  overflow: hidden;
  /* ลบ animation: fadeInUp */
}

.content-footer {
  /* Background เดียวกับ Header - แบบนิ่ง */
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #16213e 75%, #1a1a2e 100%);
  min-height: 342px;
  padding: 40px 0;
  position: relative;
  overflow: hidden;
  color: #fff;
}

.content-footer::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background:
    linear-gradient(60deg, transparent 40%, rgba(0, 198, 255, 0.03) 40%, rgba(0, 198, 255, 0.03) 60%, transparent 60%),
    linear-gradient(-60deg, transparent 40%, rgba(0, 139, 229, 0.03) 40%, rgba(0, 139, 229, 0.03) 60%, transparent 60%),
    linear-gradient(135deg,
      rgba(0, 0, 0, 0.75) 0%,
      rgba(0, 0, 0, 0.6) 30%,
      rgba(0, 0, 0, 0.5) 70%,
      rgba(0, 0, 0, 0.75) 100%);
  background-size: 80px 80px, 80px 80px, 100% 100%;
  backdrop-filter: blur(5px);
  z-index: 1;
}

.content-footer::after {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 107, 157, 0.12) 0%, transparent 40%),
    radial-gradient(circle at 85% 75%, rgba(116, 185, 255, 0.10) 0%, transparent 40%),
    radial-gradient(circle at 50% 10%, rgba(196, 69, 105, 0.08) 0%, transparent 30%);
  /* ลบ animation: float */
  z-index: 2;
}

.width-auto {
  width: var(--footer-max-width);
  margin: 0 auto;
  position: relative;
  z-index: 3;
}

.content-footer .width-auto {
  display: grid;
  grid-template-columns: 40% 55%;
  grid-column-gap: 5%;
  align-items: start;
  /* ลบ animation: fadeInUp */
}



.content-footer .content a {
  display: inline-block;
  text-align: center;
  transition: var(--footer-transition);
}

/* เก็บ Hover Effect แค่ลิงค์ */
.content-footer .content a:hover {
  transform: translateY(-2px);
  filter: drop-shadow(0 4px 8px rgba(0, 198, 255, 0.3));
}

.content-footer p {
  font-size: 14px;
  color: var(--footer-text-muted);
  font-family: 'Kanit', sans-serif;
  font-weight: 300;
  line-height: 1.6;
  margin: 15px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  padding-left: 40px;
}

.footer-site-logo {
  margin: 0 0 30px;
  text-align: center;
  position: relative;
}

.footer-site-logo img {
  width: 300px;
  max-width: 100%;
  height: auto;
  transition: var(--footer-transition);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* เก็บ Hover Effect แค่โลโก้ */
.footer-site-logo:hover img {
  transform: scale(1.02);
  filter: drop-shadow(0 6px 12px rgba(0, 198, 255, 0.3));
}

.t-footer {
  font-size: 18px;
  font-family: 'Kanit', sans-serif;
  font-weight: 400;
  color: var(--footer-white);
  margin: 10px 0 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

p.title-t {
  font-size: 24px;
  color: var(--footer-white);
  border-bottom: 2px solid var(--footer-border-light);
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-family: 'Kanit', sans-serif;
  font-weight: 400;
  position: relative;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

p.title-t::after {
  content: '';
  position: absolute;
  bottom: -2px; left: 0;
  width: 0; height: 2px;
  background: linear-gradient(90deg, var(--footer-primary), var(--footer-secondary));
  transition: width 0.4s ease;
}

/* เก็บ Hover Effect แค่ Title */
p.title-t:hover::after {
  width: 100%;
}

.footer-tag {
  margin-top: -25px;
  /* ลบ animation: slideInRight */
}

.f-tag {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.f-tag a {
  font-size: 14px;
  font-family: 'Kanit', sans-serif;
  font-weight: 400;
  color: var(--footer-text-dark);
  background: var(--footer-bg-dark);
  border-radius: 25px;
  padding: 8px 16px;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  transition: var(--footer-transition);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

/* ลบ Animation ที่ซับซ้อน - เก็บแค่ Hover ง่ายๆ */
.f-tag a:hover {
  color: var(--footer-black) !important;
  background: linear-gradient(148deg, var(--footer-primary), var(--footer-secondary), var(--footer-tertiary));
  transform: translateY(-1px);
  border-color: var(--footer-primary);
}

footer#colophon nav#site-navigation {
  border-top: 0;
  padding: 20px 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.site-info {
  display: block;
  overflow: hidden;
  text-align: center;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(15px);
  position: relative;
}

.site-info::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--footer-border), transparent);
}

.site-info p {
  border-top: 1px solid var(--footer-border);
  font-size: 14px;
  color: var(--footer-text-light);
  padding: 15px 20px;
  margin: 0;
  font-family: 'Kanit', sans-serif;
  font-weight: 300;
  line-height: 1.5;
  color:#fff;
}

.site-info a {
  color: var(--footer-success);
  font-weight: bold;
  font-family: 'Kanit', sans-serif;
  text-decoration: none;
  transition: var(--footer-transition);
  position: relative;
}

.site-info a::after {
  content: '';
  position: absolute;
  bottom: -2px; left: 0;
  width: 0; height: 2px;
  background: var(--footer-primary);
  transition: width 0.3s ease;
}

/* เก็บ Hover Effect แค่ลิงค์ */
.site-info a:hover {
  color: var(--footer-primary);
  text-shadow: 0 0 6px rgba(0, 198, 255, 0.4);
}

.site-info a:hover::after {
  width: 100%;
}

/* เพิ่ม Performance Optimization */
.content-footer,
.content-footer::before,
.content-footer::after {
  will-change: auto;
  transform: translateZ(0);
}

/* ลด GPU Usage */
.f-tag a,
.footer-site-logo img,
.content-footer .content a {
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Responsive Optimization */
@media (max-width: 768px) {
  .content-footer .width-auto {
    grid-template-columns: 1fr;
    grid-row-gap: 30px;
  }
  
  .footer-site-logo img {
    width: 250px;
  }
  
  .content-footer p {
    padding-left: 20px;
  }
  
  /* ลด Effects บน Mobile */
  .content-footer::before,
  .content-footer::after {
    display: none;
  }
  
  .f-tag a:hover {
    transform: none;
  }
  
  .footer-site-logo:hover img {
    transform: none;
  }
}

@media (max-width: 480px) {
  .content-footer {
    padding: 30px 0;
    min-height: 280px;
  }
  
  .footer-site-logo img {
    width: 200px;
  }
  
  p.title-t {
    font-size: 20px;
  }
  
  .t-footer {
    font-size: 16px;
  }
  
  .f-tag a {
    font-size: 13px;
    padding: 6px 12px;
  }
}


/* ===== RESPONSIVE DESIGN - แก้ไขปัญหาตัวอักษรขยายตัว ===== */

/* ป้องกันการปรับขนาดข้อความอัตโนมัติ */
* {
    -webkit-text-size-adjust: 100% !important;
    -ms-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
}

/* ===== TABLET RESPONSIVE (768px - 1023px) ===== */
@media (max-width: 1023px) and (min-width: 768px) {

    /* Container responsive */
    #page {
        min-width: auto !important;
        width: 100% !important;
    }

    .home #page {
        min-width: auto !important;
        width: 100% !important;
    }

    /* Header responsive */
    .header-info {
        width: 95% !important;
        max-width: 768px !important;
        margin: 0 auto !important;
        grid-template-columns: 1fr !important;
        text-align: center !important;
        gap: 15px !important;
    }

    .container-index {
        width: 95% !important;
        max-width: 768px !important;
    }

    /* Grid adjustments */
    .grid-main {
        width: 95% !important;
        max-width: 768px !important;
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }

    .grid-movie-index {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 15px !important;
    }

    .grid-movie-advise {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 10px !important;
    }

    /* Movie description */
    .movie-description {
        grid-template-columns: 200px 1fr !important;
        gap: 15px !important;
    }

    /* Search input */
    input#input-ser {
        width: 90% !important;
        max-width: 400px !important;
    }

    /* Logo */
    section.header-logo img {
        width: 180px !important;
        float: none !important;
        display: block !important;
        margin: 0 auto !important;
    }

    /* Contact button */
    .contact .hd-contact a {
        width: 150px !important;
        height: 45px !important;
        font-size: 18px !important;
    }

    /* Footer responsive */
    .width-auto {
        width: 95%;
        padding: 0 15px;
    }

    .content-footer {
        min-height: 400px;
        padding: 30px 0;
        background-attachment: scroll;
    }

    .content-footer .width-auto {
        grid-template-columns: 1fr;
        grid-row-gap: 30px;
        text-align: center;
    }

    .footer-site-logo img {
        width: 250px;
    }

    p.title-t {
        font-size: 20px;
        text-align: center;
    }

    .t-footer {
        font-size: 16px;
        text-align: center;
    }

    .content-footer p {
        text-align: center;
        font-size: 15px;
    }

    .f-tag {
        justify-content: center;
        gap: 6px;
    }

    .footer-tag {
        margin-top: 0;
    }
}

/* ===== MOBILE RESPONSIVE (max-width: 767px) ===== */
@media (max-width: 767px) {

    /* Container fixes */
    #page {
        min-width: auto !important;
        width: 100% !important;
        overflow-x: hidden !important;
    }

    .home #page {
        min-width: auto !important;
        width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Header responsive */
    .master-head {
        width: 100% !important;
        padding: 15px 10px !important;
    }

    .header-info {
        width: 100% !important;
        margin: 0 !important;
        padding: 10px !important;
        grid-template-columns: 1fr !important;
        text-align: center !important;
        gap: 15px !important;
    }

    .container-index {
        width: 100% !important;
        padding: 0 15px !important;
    }

    /* Site branding */
    .site-branding .header-description h1 {
        font-size: 20px !important;
        margin-bottom: 10px !important;
    }

    /* Logo */
    section.header-logo img {
        width: 150px !important;
        float: none !important;
        display: block !important;
        margin: 0 auto !important;
    }

    /* Search input */
    input#input-ser {
        width: 100% !important;
        max-width: 300px !important;
        font-size: 16px !important; /* ป้องกันการ zoom ใน iOS */
        padding: 10px 20px !important;
    }

    /* Contact button */
    .contact .hd-contact a {
        width: 140px !important;
        height: 40px !important;
        font-size: 16px !important;
        padding: 8px 20px !important;
    }

    /* Navigation */
    .main-navigation {
        width: 100% !important;
        padding: 0 !important;
    }

    .main-navigation li {
        display: block !important;
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .main-navigation a {
        font-size: 16px !important;
        padding: 15px 10px !important;
        margin: 2px 5px !important;
    }

    /* Grid layouts */
    .grid-main {
        width: 100% !important;
        padding: 0 15px !important;
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }

    .grid-movie-index {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 10px !important;
    }

    .grid-movie-advise {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px !important;
    }

    /* Movie description */
    .movie-description {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }

    .movie-description .thumb-img {
        text-align: center !important;
    }

    .movie-description .thumb-img img {
        max-width: 200px !important;
        width: 100% !important;
    }

    /* Movie excerpt */
    .movie-excerpt {
        padding: 15px !important;
    }

    .movie-excerpt h4 {
        font-size: 18px !important;
    }

    .movie-excerpt p {
        font-size: 14px !important;
        height: auto !important;
        -webkit-line-clamp: 8 !important;
        line-clamp: 8 !important;
    }

    /* Movie tags */
    .movie-tags a {
        width: auto !important;
        min-width: 100px !important;
        padding: 5px 15px !important;
        font-size: 13px !important;
    }

    /* Sidebar */
    .sidebar ul li a {
        font-size: 14px !important;
        padding: 12px 0 12px 15px !important;
    }

    .sidebar-header h2 {
        font-size: 16px !important;
        padding: 10px 0 !important;
    }

    /* Main content */
    #main h2 {
        font-size: 18px !important;
        padding: 10px 0 !important;
    }

    /* Movie box */
    .movie_box p {
        font-size: 13px !important;
        height: 35px !important;
        margin: 8px 5px !important;
    }

    /* Playing movie */
    iframe.res-iframe {
        height: 250px !important;
    }

    /* Trailer */
    .trailer-movie {
        padding-bottom: 56.25% !important;
    }

    /* Info blog */
    .info-blog .detail-movie {
        flex-direction: column !important;
        gap: 10px !important;
        text-align: center !important;
    }

    .info-blog .detail-movie span {
        font-size: 13px !important;
    }

    /* Pagination */
    .pagination-x {
        margin: 40px 0 !important;
    }

    span.page-numbers.current,
    a.page-numbers,
    span.page-numbers.dots {
        padding: 5px 12px !important;
        margin: 0 3px !important;
        font-size: 14px !important;
    }

    /* Footer Mobile */
    .width-auto {
        width: 95%;
        padding: 0 10px;
    }

    .content-footer {
        min-height: 350px;
        padding: 25px 0;
        background-attachment: scroll;
    }

    .content-footer::after {
        animation: none;
    }

    .content-footer .width-auto {
        grid-template-columns: 1fr;
        grid-row-gap: 25px;
        text-align: center;
    }

    .footer-site-logo img {
        width: 200px;
    }

    p.title-t {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .t-footer {
        font-size: 15px;
        margin: 8px 0 12px;
    }

    .content-footer p {
        font-size: 14px;
        margin: 12px 0;
        padding-left: 0;
    }

    .f-tag {
        gap: 5px;
        margin-top: 10px;
        justify-content: center;
    }

    .f-tag a {
        font-size: 12px;
        padding: 5px 10px;
        border-radius: 20px;
    }

    .site-info p {
        padding: 12px 15px;
        font-size: 13px;
    }

    .footer-site-logo:hover img {
        transform: scale(1.02);
    }

    .f-tag a:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 198, 255, 0.3);
    }
}

/* ===== SMALL MOBILE (max-width: 480px) ===== */
@media (max-width: 480px) {

    /* Header */
    .master-head {
        padding: 10px 5px !important;
    }

    .header-info {
        padding: 5px !important;
        gap: 10px !important;
    }

    .site-branding .header-description h1 {
        font-size: 18px !important;
    }

    /* Logo */
    section.header-logo img {
        width: 120px !important;
    }

    /* Search */
    input#input-ser {
        max-width: 250px !important;
        padding: 8px 15px !important;
    }

    /* Contact */
    .contact .hd-contact a {
        width: 120px !important;
        height: 35px !important;
        font-size: 14px !important;
        padding: 6px 15px !important;
    }

    /* Navigation */
    .main-navigation a {
        font-size: 14px !important;
        padding: 12px 8px !important;
    }

    /* Grid */
    .grid-movie-index {
        grid-template-columns: 1fr 1fr !important;
        gap: 8px !important;
    }

    .grid-movie-advise {
        grid-template-columns: 1fr 1fr !important;
        gap: 6px !important;
    }

    /* Movie */
    .movie_box p {
        font-size: 12px !important;
        height: 30px !important;
    }

    iframe.res-iframe {
        height: 200px !important;
    }

    /* Content */
    #main h2 {
        font-size: 16px !important;
    }

    .movie-excerpt h4 {
        font-size: 16px !important;
    }

    .movie-excerpt p {
        font-size: 13px !important;
    }

    /* Footer Small Mobile */
    .width-auto {
        width: 95%;
        padding: 0 8px;
    }

    .content-footer {
        min-height: 320px;
        padding: 20px 0;
    }

    .footer-site-logo img {
        width: 180px;
    }

    p.title-t {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .t-footer {
        font-size: 14px;
    }

    .content-footer p {
        font-size: 13px;
    }

    .f-tag a {
        font-size: 11px;
        padding: 4px 8px;
    }

    .site-info p {
        padding: 10px 12px;
        font-size: 12px;
    }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (max-width: 767px) and (orientation: landscape) {

    .header-info {
        grid-template-columns: 1fr 2fr 1fr !important;
        align-items: center !important;
    }

    section.header-logo img {
        width: 100px !important;
    }

    .site-branding .header-description h1 {
        font-size: 16px !important;
    }

    iframe.res-iframe {
        height: 180px !important;
    }
}

/* ===== ACCESSIBILITY & PERFORMANCE ===== */
@media (prefers-reduced-motion: reduce) {
    .site-footer,
    .content-footer .content,
    .footer-tag {
        animation: none;
    }

    .content-footer::after {
        animation: none;
    }

    .f-tag a:hover {
        animation: none;
    }

    .master-head::before,
    .master-head::after {
        animation: none;
    }
}

@media (prefers-color-scheme: dark) {
    .content-footer::before {
        background: linear-gradient(135deg,
            rgba(0, 0, 0, 0.9) 0%,
            rgba(0, 0, 0, 0.8) 30%,
            rgba(0, 0, 0, 0.7) 70%,
            rgba(0, 0, 0, 0.9) 100%);
    }
}

/* ===== HIGH DPI OPTIMIZATION ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: subpixel-antialiased;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    body {
        background: white;
        color: black;
        font-size: 12pt;
        line-height: 1.4;
    }

    .master-head {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc;
    }

    .main-navigation,
    .search-input,
    .contact {
        display: none;
    }

    .content-footer {
        background: white !important;
        color: black !important;
    }

    .f-tag,
    .footer-site-logo {
        display: none;
    }
}
<style>
/* เพิ่ม CSS เฉพาะส่วนที่จำเป็น */
.movie-slider-wrapper {
    position: relative;
    overflow: hidden;
}

.grid-movie-advise {
    display: flex;
    transition: transform 0.3s ease;
    gap: 20px;
}

.slider-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.slider-arrow:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
}

.slider-prev {
    left: 10px;
}

.slider-next {
    right: 10px;
}

.movie_box {
    flex: 0 auto;
    width: 100%;
    min-width: 162px;
}

/* Responsive */
@media (max-width: 1024px) {
    .movie_box {
        width: calc(33.333% - 14px);
    }
}

@media (max-width: 768px) {
    .movie_box {
        width: calc(50% - 10px);
    }
}

@media (max-width: 480px) {
    .movie_box {
        width: calc(100% - 0px);
    }
    .grid-movie-advise {
        gap: 0;
    }
}
</style>

<script>
let currentSlide = 0;
const movieGrid = document.getElementById('movieGrid');
const movieBoxes = movieGrid.querySelectorAll('.movie_box');
const totalMovies = movieBoxes.length;