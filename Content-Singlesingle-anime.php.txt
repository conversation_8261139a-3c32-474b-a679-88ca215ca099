<?php get_header(); ?>
<?php
$postID        = get_the_ID();
$url           = get_permalink();
$imgbg         = get_the_post_thumbnail_url(null, 'medium');
$synopsis      = get_the_excerpt();
$title_parts   = split_title($postID);
$english_title = $title_parts[0];
$imdb_rating = get_post_meta($postID, 'imdb', true);
if (!$imdb_rating) $imdb_rating = number_format(mt_rand(51, 70) / 10, 1);
$gdrivedubbed = get_post_meta($postID, 'serie_dubbed', true);
$gdrivesubbed = get_post_meta($postID, 'serie_subbed',  true);
$videoId      = get_post_meta($postID, 'trailer', true);
?>
<main id="primary" class="site-main">
    <div class="grid-main">
        <?php include get_template_directory() . '/left-sidebar.php'; ?>

        <div id="main-movie">
            <div class="title-box-movie"><h1><?php the_title(); ?></h1></div>

            <div class="informs-movie">
                <div class="trailer-movie">
                    <iframe src="https://www.youtube.com/embed/<?php echo esc_attr($videoId); ?>/" title="<?php echo esc_attr($english_title); ?>" frameborder="0" allowfullscreen style="width:706px;height:400px"></iframe>
                </div>

                <div class="h2-trailer"><h2>ตัวอย่าง : <?php the_title(); ?></h2></div>

                <div class="info-blog">
                    <div class="detail-movie">
                        <?php display_years_link(); ?>
                        <span class="movies-lang"><i class="fas fa-volume-up"></i> เสียง : <?php echo get_audio_type(get_the_ID()); ?></span>
                        <span class="movie-time"> เวลา : <span>2 ชั่วโมง</span> 13 <span>นาที</span></span>
                        <span class="star"><img src="/wp-content/uploads/2025/06/Imdb.webp" alt="IMDb"> <?php echo esc_html($imdb_rating); ?></span>
                        <span>คุณภาพ : <span class="movie-hd">HD</span></span>
                    </div>
                </div>

                <div class="movie-description">
                    <div class="thumb-img">
                        <img src="<?php echo esc_url($imgbg); ?>" width="270" height="400" alt="ดูหนัง <?php echo esc_attr($english_title); ?> เต็มเรื่องฟรี">
                    </div>
                    <div class="movie-excerpt">
                        <h4>เรื่องย่อ : <?php echo esc_html($english_title); ?></h4>
                        <p><?php echo wp_kses_post($synopsis); ?></p>
                        <?php display_tags_and_categories($postID); ?>
                    </div>
                </div>
            </div>

            <div class="playing-movie">
                <iframe src="<?php echo esc_url(home_url('/playerserie?id=' . $postID)); ?>" title="<?php echo esc_attr($english_title); ?>" class="res-iframe" frameborder="0" scrolling="no" allowfullscreen></iframe>
            </div>

			<div class="report-button-wrapper" style="text-align: center; margin-top: 20px; display: block !important;">
    <a href="https://discord.gg/tZuukHjP" target="_blank" rel="noopener"
        style="display: inline-block !important; padding: 10px 20px; color: white; text-decoration: none; font-weight: bold;">
        เข้ากลุ่ม Discord แจ้งปัญหาหนัง พูดคุย  
    </a>
</div>
 
            <div class="title-box-movie"><div class="h1-text"><h4>รายการอมนิเมะที่คุณอาจไม่เคยดู</h4></div></div>
            <div class="grid-movie-index">
                <?php $related_ids = get_related_movies($postID, 10);
                    if (!empty($related_ids)) { 
                        foreach ($related_ids as $related_id) : 
                            $post = get_post($related_id); 
                            setup_postdata($post); ?>
                            <div class="movie_box">
                                <a id="index-grid" href="<?php the_permalink(); ?>">
                                    <?php if (has_post_thumbnail()) { ?>
                                        <img src="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'medium')); ?>" alt="<?php the_title_attribute(); ?>">
                                    <?php } ?>
                                    <p><?php custom_the_title(); ?></p>
                                    <span class="movie-lang">
                                        <?php echo get_audio_type(get_the_ID()); ?>
                                    </span>
                                    <div class="figure-box">
                                        <span class="box-movie-star"><?php display_imdb_rating(); ?></span>
                                        <span class="box-movie-hd">HD</span>
										
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; 
                    wp_reset_postdata();}?>
            </div>
        </div>
        <?php get_sidebar(); ?>
    </div>
</main>
<?php get_footer(); ?>