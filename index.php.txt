<?php get_header(); ?>
<main id="primary" class="site-main">
    <div class="container-index">
		<?php if (is_front_page() && is_home() && !is_paged()) { ?>
        <div class="idx-rec">
            <div class="nds-title">
                <h2><?php if (shortcode_exists('mytheme_main_title')) { echo do_shortcode('[mytheme_main_title]'); } ?></h2>
            </div>

            <!-- Recommend Movies Slider -->
            <div class="recommend-slider-wrapper">
                <button class="recommend-arrow recommend-prev" aria-label="Previous">
                    <i class="arrow-icon">‹</i>
                </button>

                <div class="recommend-slider">
                    <div class="recommend-slides" id="recommend-slides">
                        <?php $oz_press = new WP_Query("showposts=16&post_type=movie&category_name=recommend"); while ($oz_press->have_posts()) : $oz_press->the_post(); ?>
                            <div class="recommend-slide-item">
                                <a href="<?php the_permalink() ?>">
                                    <div class="recommend-poster">
                                        <?php if (has_post_thumbnail()) { ?>
                                            <img alt="<?php the_title(); ?>" src="<?php echo the_post_thumbnail_url('medium'); ?>">
                                        <?php } ?>
                                        <div class="recommend-overlay">
                                            <div class="recommend-rating">
                                                <i class="star-icon">★</i>
                                                <span><?php display_imdb_rating(); ?></span>
                                            </div>
                                            <div class="recommend-quality">HD</div>
                                        </div>
                                    </div>
                                    <div class="recommend-info">
                                        <h3><?php custom_the_title(); ?></h3>
                                        <span class="recommend-audio"><?php echo get_audio_type(get_the_ID()); ?></span>
                                    </div>
                                </a>
                            </div>
                        <?php endwhile; wp_reset_postdata(); ?>
                    </div>
                </div>

                <button class="recommend-arrow recommend-next" aria-label="Next">
                    <i class="arrow-icon">›</i>
                </button>
            </div>
        </div>
		<?php } ?>
		<div class="mp4hd-content"><?php if(shortcode_exists('mytheme_header_description')){ echo do_shortcode('[mytheme_header_description]'); } ?></div>
		<div class="grid-main">
            <?php include('left-sidebar.php'); ?>
            <div id="main">
                <div class="update-movie">
                    <h2><?php if (shortcode_exists('mytheme_reserve_title')) { echo do_shortcode('[mytheme_reserve_title]'); } ?></h2>
                </div>
                <div class="grid-movie-index">
                	<?php if (have_posts()) : ?>
                    	<?php while (have_posts()) : the_post(); ?>
                            <div class="movie_box">
                                <a id="index-grid" href="<?php the_permalink() ?>">
                                    <div class="figure-box">
                                        <?php if (has_post_thumbnail()) { ?>
                                            <img alt="<?php the_title(); ?>" src="<?php echo the_post_thumbnail_url('medium'); ?>">
                                        <?php } ?>
                                        <span class="box-movie-star"><?php display_imdb_rating(); ?></span>
                                        <span class="box-movie-hd">HD</span>
                                       
                                    </div>
                                    <p><?php custom_the_title(); ?></p>
                                    <span class="movie-lang">
                                        <?php echo get_audio_type(get_the_ID()); ?>
                                    </span>
                                </a>
                            </div>
                        <?php endwhile; ?>
                        <?php wp_reset_postdata(); ?>
                    <?php endif; ?>
                </div>
                <?php custom_pagination(); ?>
            </div>
            <?php get_sidebar(); ?>
        </div>
    </div>
</main>
<?php get_footer(); ?>
