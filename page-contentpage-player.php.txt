<?php
$postID = $_GET['id'];
$gdrivedubbed = get_post_meta($postID, 'video_id_thai', true);
$gdrivesubbed = get_post_meta($postID, 'video_id_sub', true);
$seriesepisodes = get_post_meta($postID, '_series_episodes', true);
function extract_drive_id($url) {
    preg_match('/file\/d\/(.*?)\/view/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : '';
}
function generate_md5_embed_url($drive_id) {
    $md5_encoded = md5($drive_id);
    return home_url() . '/embed/' . $md5_encoded . '/';
}
?>
<?php if (!empty($gdrivedubbed) || !empty($gdrivesubbed)) { ?> 
<style type="text/css">
    body {margin: 0; padding: 0; height: 100%; float: left; width: 100%;background: #0a0a0a;}
    #moviePlayer iframe {height: 494px; width: 100%;}
    #moviePlayer {float: left; display: grid; width: 100%; margin: 0; padding: 0;}
    #group-url {float: left; margin: 5px;}
    #group-sound {float: right; margin: 5px;}
    #group-url .mov_source i {color: #4a0b80;}
    #group-url .active i {color: #ffffff;}
    button {font-family: kanit; font-weight: 500; color: #999999; background: #202020; border-radius: 20px; cursor: pointer; min-width: unset; border: 0; padding: 7px 25px;}
    button.active {background:  linear-gradient(148deg, #00a799, #65ffaa, #d3ffe7); color: #1c1b1b; font-weight: 500; border: 0; font-family: kanit; padding: 7px 25px; min-width: unset;}
</style>
<div id="group-url">
    <button class="mov_source active" data-id="0">เล่นหลัก</button>
</div>
<div id="moviePlayer" class="moviePlayer">
    <div class="loading" id="loadingpage" style="display: none;"></div>
    <iframe src="<?php echo home_url(); ?>/embed/<?php if (!empty($gdrivedubbed)) { echo $gdrivedubbed;} else { echo $gdrivesubbed;} ?>/" title="<?php echo $english_title;?>" allowfullscreen="" frameborder="0" scrolling="no" class="res-iframe"></iframe>
</div>
<div id="group-sound">
    <?php if (!empty($gdrivedubbed)) { ?>
    <button class="mov_sound active" data-sound="thai"> พากย์ไทย</button>
    <?php } else if (!empty($gdrivesubbed)) { ?>
    <button class="mov_sound active" data-sound="subthai"> ซับไทย</button>
    <?php } ?>
</div>
<?php } elseif (!empty($seriesepisodes)) { ?>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.9/css/bootstrap-select.min.css">
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.9/js/bootstrap-select.min.js"></script>
    <style>
		body {margin: 0;padding: 0;height: 100%;float: left;width: 100%;background: #0a0a0a;}
		#moviePlayer {float: left;display: grid;width: 100%;margin: 0;padding: 0;}
		#moviePlayer iframe {height: 494px;width: 100%;}
        #video_container1 {height: 490px !important;}
        .player-embed.dropdown {height: 480px !important;}
        .player-embed.list {height: 300px !important;}
        #video_container1.list {height: 340px !important;}
        button {border: solid 1px #4a0b80; border-radius: 5px; background: #202020; color: #fff;}
        #group-url {float: left; width: 60%; text-align: left;}
        button.mov_source, button.mov_sound {color: #fff; font-weight: 500; border: 0; border-radius: 20px; font-family: kanit; padding: 7px 25px; min-width: unset;}
        #group-url .mov_source i {color: #4a0b80; margin-right: 10px;}
        #group-url .active i {color: #fff!important; margin-right: 10px;}
        button.active {background: linear-gradient(148deg, #00a799, #65ffaa, #d3ffe7); !important; color: #1c1b1b; font-weight: 500; margin: 5px; border: 0; border-radius: 20px; font-family: kanit; padding: 7px 25px; min-width: unset;}
        #group-sound {margin-top: 0; float: right; width: 40%; text-align: right;}
        #group-sound.active {background: #fe0000!important;}
        .btn.focus, .btn:focus, .btn:hover {color: #fff; text-decoration: none;}
        .dropdown-menu {border-radius: 10px !important; background-color: unset !important;}
        .bootstrap-select .dropdown-menu li a {font-family: kanit; padding: 10px 15px; border: 0; color: #fff; text-align: center; background-image: unset; background-color: #181818; outline: none;}
        .bootstrap-select .dropdown-menu li a:hover {color: #ffc917; background-image: unset !important; background-color: #212120 !important;}
        .bootstrap-select>.dropdown-toggle, .bootstrap-select>.dropdown-toggle:focus {background: linear-gradient(148deg, #00a799, #65ffaa, #d3ffe7); padding: 10px 25px; border-radius: 11px; color: #1c1b1b; outline: none !important; min-width: unset; height: unset; margin-top: 5%; box-shadow: unset; border: 0; font-family: kanit; font-size: 15px; font-weight: 500;}
        .bootstrap-select .dropdown-menu li.active a {font-family: kanit; font-weight: 400; padding: 10px 15px; color: #ffc917; background-image: unset !important; background-color: #212120 !important; border: 0;}
        .bootstrap-select .inner::-webkit-scrollbar {width: 6px; background-color: #F5F5F5;}
        .bootstrap-select .inner::-webkit-scrollbar-thumb {border-radius: 10px; -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3); background-color: #262626a3;}
        .bootstrap-select .inner::-webkit-scrollbar-track {-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; background-color: #464646;}
        .playlist-content {width: 100%; height: 100px; padding-right: 14px; float: right;}
        .playlist-ep {overflow: hidden; -webkit-box-flex: 1; float: left; flex: 1 1 auto; height: 130px; width: 50%;}
        .playlist-ep-header {padding: 15px 15px 5px; margin-bottom: 0; color: #ffffff; font-weight: 600;}
        .playlist-ep-body {overflow: hidden; overflow-y: scroll; height: 100px; padding: 0 15px;}
        .select-ep {padding: 5px 10px; cursor: pointer; border-bottom: 1px dotted #ddd;}
        .select-ep:hover {color: #f24c50;}
        .playlist-ss {overflow: hidden; -webkit-box-flex: 1; float: left; flex: 1 1 auto; height: 130px; width: 50%;}
        .playlist-ss-header {padding: 15px 15px 5px; margin-bottom: 0; color: #ffffff; font-weight: 600;}
        .playlist-ss-body {overflow: hidden; overflow-y: scroll; height: 100px; padding: 0 15px;}
        .select-ss {padding: 5px 0; cursor: pointer;}
        .select-ss:hover {color: #f24c50;}
        .playlist-scroll::-webkit-scrollbar {width: 6px; border-radius: 10px;}
        .playlist-scroll::-webkit-scrollbar-thumb {border-radius: 10px; -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3); background-color: #b3b3b380;}
        .playlist-scroll::-webkit-scrollbar-track {-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; background-color: #63636300;}
        .arrow {border: solid black; border-width: 0 3px 3px 0; display: inline-block; padding: 3px; transform: rotate(-45deg); -webkit-transform: rotate(-45deg);}
    </style>
	<div id="group-url"><button class="mov_source active" data-id="0">เล่นหลัก</button></div>
    <div id="moviePlayer" class="moviePlayer">
        <div class="loading" id="loadingpage" style="display: none;"></div>
        <?php 
        $first_drive_id = extract_drive_id($seriesepisodes[0]); 
        if ($first_drive_id) { 
            $first_embed_url = generate_md5_embed_url($first_drive_id); 
        ?>
            <iframe src="<?php echo $first_embed_url; ?>" title="Episode Player" allowfullscreen="" frameborder="0" scrolling="no" class="res-iframe"></iframe>
        <?php } ?>
    </div>
    <div class="pull-right">
        <select class="selectpicker select-theme" id="series-episode" name="series-episode" data-style="form-control form-control-custom">
            <?php 
            $episode_number = 1; 
            foreach ($seriesepisodes as $episode_link) {
                $drive_id = extract_drive_id($episode_link);
                if ($drive_id) {
                    $embed_url = generate_md5_embed_url($drive_id);
            ?>
                    <option value="<?php echo $embed_url; ?>">ตอนที่ <?php echo $episode_number; ?></option>
            <?php 
                    $episode_number++;
                }
            } 
            ?>
        </select>
    </div>
    <script>
        $('#series-episode').on('change', function() {
            var episodeUrl = $(this).val();
            $('#moviePlayer iframe').attr('src', episodeUrl);
        });
    </script>
<?php } ?>
