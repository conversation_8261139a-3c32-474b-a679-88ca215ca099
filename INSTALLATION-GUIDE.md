# 🚀 คำแนะนำการติดตั้ง Recommend Movies Slider

## 📁 ไฟล์ที่ได้รับ

1. **`functions-recommend-slider.php`** - ฟังก์ชันสำหรับ slider
2. **`index-new.php`** - ไฟล์ index.php ใหม่
3. **`INSTALLATION-GUIDE.md`** - คำแนะนำนี้

## 🔧 วิธีติดตั้ง

### **ขั้นตอนที่ 1: แก้ไข functions.php**

เปิดไฟล์ `functions.php` ของคุณและเพิ่มโค้ดต่อไปนี้ **ต่อท้ายไฟล์** (ก่อน `?>` สุดท้าย):

```php
// Include Movie Slider functionality
require_once get_template_directory() . '/movie-slider.php';

// Enqueue Movie Slider assets
function enqueue_movie_slider_assets() {
    wp_enqueue_style('movie-slider-css', get_template_directory_uri() . '/movie-slider.css', array(), '1.0.0');
    wp_enqueue_script('movie-slider-js', get_template_directory_uri() . '/movie-slider.js', array(), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('movie-slider-js', 'movieSliderAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('movie_slider_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_movie_slider_assets');

// Shortcode สำหรับแสดง Movie Slider
function movie_slider_shortcode($atts) {
    $atts = shortcode_atts(array(
        'title' => 'หนังใหม่',
        'show_title' => 'true'
    ), $atts);
    
    ob_start();
    
    if ($atts['show_title'] === 'true') {
        echo '<div class="slider-section-title">';
        echo '<h2>' . esc_html($atts['title']) . '</h2>';
        echo '<a href="#" class="view-all-link">ดูทั้งหมด <span class="arrow">→</span></a>';
        echo '</div>';
    }
    
    display_movie_slider();
    
    return ob_get_clean();
}
add_shortcode('movie_slider', 'movie_slider_shortcode');

// Add Recommend Slider CSS
function add_recommend_slider_styles() {
    ?>
    <style>
    /* Recommend Movies Slider */
    .recommend-slider-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        gap: 15px;
        margin: 20px 0;
    }

    .recommend-arrow {
        background: rgba(0, 0, 0, 0.7);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        z-index: 10;
        position: relative;
    }

    .recommend-arrow:hover {
        background: rgba(255, 107, 53, 0.9);
        border-color: rgba(255, 255, 255, 0.8);
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
    }

    .recommend-arrow:disabled {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.1);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        opacity: 0.5;
    }

    .recommend-arrow .arrow-icon {
        font-size: 20px;
        font-weight: bold;
        color: white;
        line-height: 1;
    }

    .recommend-slider {
        flex: 1;
        overflow: hidden;
        border-radius: 10px;
        width: calc(8 * 215px);
        max-width: 100%;
    }

    .recommend-slides {
        display: flex;
        gap: 15px;
        transition: transform 0.4s ease;
        padding: 10px 0;
    }

    .recommend-slide-item {
        flex: 0 0 200px;
        background: #1a1a1a;
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        height: 320px;
    }

    .recommend-slide-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    }

    .recommend-slide-item a {
        display: block;
        text-decoration: none;
        color: inherit;
    }

    .recommend-poster {
        position: relative;
        width: 100%;
        height: 240px;
        overflow: hidden;
    }

    .recommend-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .recommend-slide-item:hover .recommend-poster img {
        transform: scale(1.05);
    }

    .recommend-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent, rgba(0,0,0,0.7));
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .recommend-slide-item:hover .recommend-overlay {
        opacity: 1;
    }

    .recommend-rating {
        display: flex;
        align-items: center;
        gap: 5px;
        background: rgba(255, 107, 53, 0.9);
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        align-self: flex-start;
    }

    .recommend-rating .star-icon {
        color: #ffd700;
    }

    .recommend-quality {
        background: rgba(0, 0, 0, 0.8);
        color: #00ff00;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        font-weight: bold;
        align-self: flex-end;
    }

    .recommend-info {
        padding: 15px;
        background: #1a1a1a;
    }

    .recommend-info h3 {
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        margin: 0 0 8px 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .recommend-audio {
        font-size: 12px;
        color: #ff6b35;
        font-weight: 500;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .recommend-slide-item {
            flex: 0 0 200px;
            height: 320px;
        }
        
        .recommend-poster {
            height: 240px;
        }
        
        .recommend-arrow {
            width: 50px;
            height: 50px;
        }
        
        .recommend-arrow .arrow-icon {
            font-size: 18px;
        }
        
        .recommend-slider-wrapper {
            gap: 10px;
        }
    }

    @media (max-width: 480px) {
        .recommend-slide-item {
            flex: 0 0 200px;
            height: 320px;
        }
        
        .recommend-poster {
            height: 240px;
        }
        
        .recommend-info {
            padding: 15px;
        }
        
        .recommend-info h3 {
            font-size: 14px;
        }
        
        .recommend-slider-wrapper {
            gap: 8px;
        }
    }
    </style>
    <?php
}
add_action('wp_head', 'add_recommend_slider_styles');
```

### **ขั้นตอนที่ 2: เพิ่ม JavaScript**

เพิ่มต่อจากโค้ด CSS ข้างบน:

```php
// Add Recommend Slider JavaScript
function add_recommend_slider_script() {
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const recommendSlider = document.querySelector('.recommend-slides');
        const prevBtn = document.querySelector('.recommend-prev');
        const nextBtn = document.querySelector('.recommend-next');
        
        if (!recommendSlider || !prevBtn || !nextBtn) return;
        
        let currentSlide = 0;
        const moviesPerSlide = 8;
        const slideWidth = 215;
        
        function getTotalSlides() {
            return recommendSlider.querySelectorAll('.recommend-slide-item').length;
        }
        
        function getTotalSlideGroups() {
            const totalMovies = getTotalSlides();
            return Math.ceil(totalMovies / moviesPerSlide);
        }
        
        function updateSliderPosition() {
            const translateX = -currentSlide * (slideWidth * moviesPerSlide);
            recommendSlider.style.transform = `translateX(${translateX}px)`;
        }
        
        function updateArrowStates() {
            const totalGroups = getTotalSlideGroups();
            
            prevBtn.disabled = currentSlide <= 0;
            nextBtn.disabled = currentSlide >= (totalGroups - 1);
            
            prevBtn.style.opacity = currentSlide <= 0 ? '0.5' : '1';
            nextBtn.style.opacity = currentSlide >= (totalGroups - 1) ? '0.5' : '1';
        }
        
        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                updateSliderPosition();
                updateArrowStates();
            }
        }
        
        function nextSlide() {
            const totalGroups = getTotalSlideGroups();
            
            if (currentSlide < (totalGroups - 1)) {
                currentSlide++;
                updateSliderPosition();
                updateArrowStates();
            }
        }
        
        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);
        
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevSlide();
            if (e.key === 'ArrowRight') nextSlide();
        });
        
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        recommendSlider.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            isDragging = true;
        });
        
        recommendSlider.addEventListener('touchmove', function(e) {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });
        
        recommendSlider.addEventListener('touchend', function() {
            if (!isDragging) return;
            
            const diffX = startX - currentX;
            const threshold = 50;
            
            if (Math.abs(diffX) > threshold) {
                if (diffX > 0) {
                    nextSlide();
                } else {
                    prevSlide();
                }
            }
            
            isDragging = false;
        });
        
        window.addEventListener('resize', function() {
            updateSliderPosition();
            updateArrowStates();
        });
        
        updateArrowStates();
    });
    </script>
    <?php
}
add_action('wp_footer', 'add_recommend_slider_script');
```

### **ขั้นตอนที่ 3: แทนที่ index.php**

Copy โค้ดทั้งหมดจากไฟล์ `index-new.php` ไปแทนที่ในไฟล์ `index.php` เดิมของคุณ

## ✅ การทดสอบ

1. Save ไฟล์ทั้งหมด
2. รีเฟรชหน้าเว็บ
3. ตรวจสอบว่า slider ทำงานได้
4. ทดสอบลูกศรซ้าย-ขวา
5. ทดสอบบนมือถือ

## 🎯 ผลลัพธ์ที่ได้

- หนังแนะนำแสดง 8 เรื่องต่อหน้าจอ
- ลูกศรทันสมัยแบบวงกลมใส
- สไลด์ทีละ 8 เรื่อง
- รองรับ touch/swipe บนมือถือ
- รองรับ keyboard navigation

## 🔧 หากมีปัญหา

1. ตรวจสอบ Console errors
2. ตรวจสอบว่ามี category "recommend"
3. ตรวจสอบว่ามีหนังในหมวดหมู่ recommend
4. ล้าง cache (ถ้ามี)
