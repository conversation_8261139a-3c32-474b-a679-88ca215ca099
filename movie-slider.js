/**
 * Movie Slider JavaScript
 * จัดการการทำงานของ Movie Category Slider
 */

class MovieSlider {
    constructor() {
        this.currentSlide = 0;
        this.slidesPerView = this.getSlidesPerView();
        this.totalSlides = 0;
        this.isLoading = false;
        
        this.init();
        this.bindEvents();
        this.handleResize();
    }
    
    init() {
        this.slider = document.querySelector('.movie-slides');
        this.prevBtn = document.querySelector('.slider-prev');
        this.nextBtn = document.querySelector('.slider-next');
        this.loadingEl = document.getElementById('slider-loading');
        this.categoryTabs = document.querySelectorAll('.category-tab');
        
        if (this.slider) {
            this.updateSlideCount();
            this.updateArrowStates();
        }
    }
    
    bindEvents() {
        // Category tab clicks
        this.categoryTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                if (this.isLoading) return;
                
                const category = e.target.dataset.category;
                this.loadCategoryMovies(category, e.target);
            });
        });
        
        // Arrow clicks
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prevSlide());
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.nextSlide());
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.prevSlide();
            if (e.key === 'ArrowRight') this.nextSlide();
        });
        
        // Touch/swipe support
        this.addTouchSupport();
    }
    
    handleResize() {
        window.addEventListener('resize', () => {
            this.slidesPerView = this.getSlidesPerView();
            this.currentSlide = 0;
            this.updateSliderPosition();
            this.updateArrowStates();
        });
    }
    
    getSlidesPerView() {
        const width = window.innerWidth;
        if (width <= 480) return 2;
        if (width <= 768) return 3;
        if (width <= 1024) return 4;
        return 5;
    }
    
    updateSlideCount() {
        const slides = this.slider.querySelectorAll('.movie-slide-item');
        this.totalSlides = slides.length;
    }
    
    loadCategoryMovies(categorySlug, tabElement) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        // Update active tab
        this.categoryTabs.forEach(tab => tab.classList.remove('active'));
        tabElement.classList.add('active');
        
        // AJAX request
        const formData = new FormData();
        formData.append('action', 'load_category_movies');
        formData.append('category_slug', categorySlug);
        
        fetch(ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.slider.innerHTML = data.data;
                this.currentSlide = 0;
                this.updateSlideCount();
                this.updateSliderPosition();
                this.updateArrowStates();
            } else {
                this.showError(data.data || 'เกิดข้อผิดพลาดในการโหลดข้อมูล');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showError('เกิดข้อผิดพลาดในการเชื่อมต่อ');
        })
        .finally(() => {
            this.isLoading = false;
            this.hideLoading();
        });
    }
    
    prevSlide() {
        if (this.currentSlide > 0) {
            this.currentSlide--;
            this.updateSliderPosition();
            this.updateArrowStates();
        }
    }
    
    nextSlide() {
        const maxSlide = Math.max(0, this.totalSlides - this.slidesPerView);
        if (this.currentSlide < maxSlide) {
            this.currentSlide++;
            this.updateSliderPosition();
            this.updateArrowStates();
        }
    }
    
    updateSliderPosition() {
        if (!this.slider) return;
        
        const slideWidth = 200 + 15; // width + gap
        const translateX = -this.currentSlide * slideWidth;
        
        this.slider.style.transform = `translateX(${translateX}px)`;
    }
    
    updateArrowStates() {
        if (!this.prevBtn || !this.nextBtn) return;
        
        const maxSlide = Math.max(0, this.totalSlides - this.slidesPerView);
        
        this.prevBtn.disabled = this.currentSlide <= 0;
        this.nextBtn.disabled = this.currentSlide >= maxSlide;
        
        // Update visual states
        this.prevBtn.style.opacity = this.currentSlide <= 0 ? '0.5' : '1';
        this.nextBtn.style.opacity = this.currentSlide >= maxSlide ? '0.5' : '1';
    }
    
    addTouchSupport() {
        if (!this.slider) return;
        
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        this.slider.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });
        
        this.slider.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });
        
        this.slider.addEventListener('touchend', () => {
            if (!isDragging) return;
            
            const diffX = startX - currentX;
            const threshold = 50;
            
            if (Math.abs(diffX) > threshold) {
                if (diffX > 0) {
                    this.nextSlide();
                } else {
                    this.prevSlide();
                }
            }
            
            isDragging = false;
        });
    }
    
    showLoading() {
        if (this.loadingEl) {
            this.loadingEl.style.display = 'flex';
        }
        
        if (this.slider) {
            this.slider.style.opacity = '0.5';
        }
    }
    
    hideLoading() {
        if (this.loadingEl) {
            this.loadingEl.style.display = 'none';
        }
        
        if (this.slider) {
            this.slider.style.opacity = '1';
        }
    }
    
    showError(message) {
        if (this.slider) {
            this.slider.innerHTML = `
                <div class="slider-error">
                    <p>⚠️ ${message}</p>
                    <button onclick="location.reload()">ลองใหม่</button>
                </div>
            `;
        }
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.movie-slider-container')) {
        new MovieSlider();
    }
});

// Global function for manual initialization
window.initMovieSlider = () => {
    return new MovieSlider();
};

// Add error styles
const errorStyles = `
    .slider-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        color: #999;
        text-align: center;
        width: 100%;
    }
    
    .slider-error p {
        margin-bottom: 15px;
        font-size: 16px;
    }
    
    .slider-error button {
        background: #ff6b35;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-family: 'Kanit', sans-serif;
    }
    
    .slider-error button:hover {
        background: #e55a2b;
    }
`;

// Inject error styles
const styleSheet = document.createElement('style');
styleSheet.textContent = errorStyles;
document.head.appendChild(styleSheet);
