<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
    <title><?php wp_title(); ?></title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=1440, initial-scale=1.0, user-scalable=yes">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="icon" href="<?php echo home_url(); ?>/wp-content/uploads/2024/01/24icon.png" type="image/x-icon">
	<link rel='stylesheet' href='<?php echo home_url(); ?>/wp-content/themes/UnderHD/style.css?ver=1.06' type='text/css' media='all' />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RYHB99MMR4"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-RYHB99MMR4');
    </script>
</head>

<body class="home">
    <div id="page">
        <header class="master-head">
            <div class="site-branding">
                <div class="header-info">
                    <section class="header-logo">
                        <a href="<?php echo home_url(); ?>"><?php if(shortcode_exists('mytheme_site_logo')){ echo do_shortcode('[mytheme_site_logo]'); } ?></a>
                    </section>
                    <section class="header-description">
						<h1><?php if(shortcode_exists('mytheme_site_header')){ echo do_shortcode('[mytheme_site_header]'); } ?></h1>
						<div class="search-input">
							<form role="search" method="GET" id="searchform" action="<?php echo home_url(); ?>">
								<input type="text" value="" placeholder="ค้นหา..." name="s" id="input-ser">
							</form>
						</div>
					</section>
                    <section class="contact">
						<div class="hd-contact"> <a href="<?php echo home_url(); ?>/contact/">ติดต่อเรา</a></div>
                    </section>
                </div>
            </div>
    		<?php if(shortcode_exists('mytheme_header_menu')){ echo do_shortcode('[mytheme_header_menu]'); } ?>
        </header>
    </div>
</body>
</html>