# 🎬 Movie Category Slider

ระบบแสดงหนังแบบสไลด์ตามหมวดหมู่ พร้อมลูกศรเลื่อนและการโหลดแบบ AJAX

## ✨ ฟีเจอร์

- 📱 **Responsive Design** - ปรับขนาดตามหน้าจอ
- 🎯 **Category Tabs** - แท็บหมวดหมู่ที่คลิกได้
- ⬅️➡️ **Navigation Arrows** - ลูกศรซ้าย-ขวาสำหรับเลื่อนดู
- 🔄 **AJAX Loading** - โหลดหนังแบบไม่ต้องรีเฟรชหน้า
- 👆 **Touch Support** - รองรับการปัดบนมือถือ
- ⌨️ **Keyboard Navigation** - ใช้ลูกศรบนคีย์บอร์ดได้
- 🎨 **Modern UI** - ดีไซน์สวยงามแบบโมเดิร์น

## 📁 ไฟล์ที่เกี่ยวข้อง

```
movie-slider.php      - PHP functions และ AJAX handlers
movie-slider.css      - Styles สำหรับ slider
movie-slider.js       - JavaScript functionality
functions.php         - เพิ่ม shortcode และ enqueue assets
index.php             - ตัวอย่างการใช้งาน
```

## 🚀 การติดตั้ง

### 1. อัพโหลดไฟล์
วางไฟล์ทั้งหมดในโฟลเดอร์ theme ของคุณ:
```
/wp-content/themes/your-theme/
├── movie-slider.php
├── movie-slider.css
├── movie-slider.js
└── functions.php (แก้ไขแล้ว)
```

### 2. ตรวจสอบ functions.php
ไฟล์ `functions.php` ได้เพิ่มโค้ดต่อไปนี้แล้ว:
```php
// Include Movie Slider functionality
require_once get_template_directory() . '/movie-slider.php';

// Enqueue assets
add_action('wp_enqueue_scripts', 'enqueue_movie_slider_assets');

// Shortcode
add_shortcode('movie_slider', 'movie_slider_shortcode');
```

## 📖 การใช้งาน

### Shortcode พื้นฐาน
```php
<?php echo do_shortcode('[movie_slider]'); ?>
```

### Shortcode พร้อม options
```php
<?php echo do_shortcode('[movie_slider title="หนังใหม่ล่าสุด"]'); ?>
```

### ใช้ในไฟล์ PHP
```php
// แสดง slider พร้อมหัวข้อ
echo do_shortcode('[movie_slider title="หนังยอดนิยม"]');

// แสดง slider โดยไม่มีหัวข้อ
echo do_shortcode('[movie_slider show_title="false"]');
```

### ใช้ใน WordPress Editor
```
[movie_slider title="หนังแนะนำ"]
```

## ⚙️ Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `title` | "หนังใหม่" | หัวข้อของ slider |
| `show_title` | "true" | แสดงหัวข้อหรือไม่ |

## 🎨 การปรับแต่ง CSS

### เปลี่ยนสีหลัก
```css
.category-tab.active {
    background: linear-gradient(135deg, #your-color, #your-color-2);
}

.slider-arrow {
    background: linear-gradient(135deg, #your-color, #your-color-2);
}
```

### ปรับขนาด slider
```css
.movie-slide-item {
    flex: 0 0 250px; /* เปลี่ยนจาก 200px */
}

.movie-poster {
    height: 350px; /* เปลี่ยนจาก 280px */
}
```

### เปลี่ยน font
```css
.movie-slider-container {
    font-family: 'Your-Font', sans-serif;
}
```

## 📱 Responsive Breakpoints

- **Desktop (1024px+)**: 5 หนังต่อหน้าจอ
- **Tablet (768px-1024px)**: 4 หนังต่อหน้าจอ
- **Mobile (480px-768px)**: 3 หนังต่อหน้าจอ
- **Small Mobile (480px-)**: 2 หนังต่อหน้าจอ

## 🔧 การแก้ไขปัญหา

### หนังไม่แสดง
1. ตรวจสอบว่ามี post type "movie" และมี posts
2. ตรวจสอบว่า categories มี posts
3. ดู Console เพื่อหา JavaScript errors

### AJAX ไม่ทำงาน
1. ตรวจสอบว่า `wp_enqueue_script` ทำงาน
2. ตรวจสอบ `admin-ajax.php` path
3. ดู Network tab ใน Developer Tools

### Slider ไม่เลื่อน
1. ตรวจสอบว่า JavaScript โหลดแล้ว
2. ตรวจสอบ CSS conflicts
3. ดู Console errors

## 🎯 ตัวอย่างการใช้งานจริง

### ในหน้าแรก (index.php)
```php
<?php get_header(); ?>

<!-- Hero Section -->
<div class="hero-section">
    <!-- Hero content -->
</div>

<!-- Movie Slider -->
<?php echo do_shortcode('[movie_slider title="หนังใหม่ล่าสุด"]'); ?>

<!-- Other content -->
<div class="main-content">
    <!-- Main content -->
</div>

<?php get_footer(); ?>
```

### ในหน้า Category
```php
<?php echo do_shortcode('[movie_slider title="หนังในหมวดหมู่นี้" show_title="true"]'); ?>
```

## 🔄 การอัพเดท

เมื่อมีการอัพเดท:
1. สำรองไฟล์เดิม
2. แทนที่ไฟล์ใหม่
3. ล้าง cache (ถ้ามี)
4. ทดสอบการทำงาน

## 📞 การสนับสนุน

หากมีปัญหาหรือต้องการปรับแต่งเพิ่มเติม:
- ตรวจสอบ Console errors
- ดู Network requests
- ตรวจสอบ PHP error logs
