<?php get_header(); ?>
<div id="wrap-content">
	<?php include('left-sidebar.php'); ?>
	<div class="mainmovie">
		<div class="archives-movie">
			<h1>หมวดหมู่ : <span class="red">Top iMDB</span></h1>
		</div>
		<div class="listmovie">
			<div class="box-movie">
				<?php
                $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
                $args = array(
					'post_type' => 'post',
                    'meta_key' => 'imdb_rating',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC',
                    'paged' => $paged
                );
                $the_query = new WP_Query($args);
                ?>
                <?php if ($the_query->have_posts()) : ?>
                    <?php while ($the_query->have_posts()) : $the_query->the_post(); ?>
                        <div class="movie-grid">
                            <div class="movie-box">
                                <?php display_imdb_rating(); ?>
                                <?php if (in_category('หนังซูมชนโรง')) { ?><div class="post-corner movie-ZM">Zoom</div><?php } else { ?><div class="post-corner movie-HD">HD</div><?php } ?>
                                <?php if (has_post_thumbnail()) { ?>
                                    <div class="movie-image"><a href="<?php the_permalink() ?>" rel="movie" title="<?php the_title(); ?>"><img alt="<?php the_title(); ?>" src="<?php echo the_post_thumbnail_url('medium'); ?>"></a></div>
                                <?php } ?>
                                <div class="p-box">
                                    <div class="p1">
                                        <?php
                                        $drivethai = get_post_meta($post->ID, 'gdrivethai', true);
                                        $drivesub = get_post_meta($post->ID, 'gdrivesub', true);
                                        if (!empty($drivethai)) {
                                            echo "เสียงไทย HD";
                                        } elseif (!empty($drivesub)) {
                                            echo "ซับไทย HD";
                                        } else {
                                            echo "รอการแก้ไข";
                                        }
                                        ?>
                                    </div>
                                    <div class="p2"><?php the_title(); ?></div>
                                </div>
                            </div>
                        </div>
					<?php endwhile; ?>
				<?php endif; ?>
			</div>
		</div>
		<div class="pagenavi">
			<div class="boxnavi">
				<div class="navigation">
					<?php wp_pagenavi(array('query' => $the_query)); ?>
				</div>
			</div>
		</div>
	</div>
	<?php get_sidebar(); ?>
</div>
<?php get_footer(); ?>