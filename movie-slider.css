/* Movie Slider Styles */
.movie-slider-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 0;
    font-family: 'Kanit', sans-serif;
}

/* Section Title */
.slider-section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 0 10px;
}

.slider-section-title h2 {
    font-size: 28px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.view-all-link {
    color: #999;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.view-all-link:hover {
    color: #ff6b35;
}

.view-all-link .arrow {
    transition: transform 0.3s ease;
}

.view-all-link:hover .arrow {
    transform: translateX(5px);
}

/* Category Tabs */
.category-tabs {
    margin-bottom: 30px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
    display: none;
}

.tabs-wrapper {
    display: flex;
    gap: 10px;
    min-width: max-content;
    padding: 0 10px;
}

.category-tab {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    color: #ffffff;
    border: 1px solid #333;
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.category-tab:hover {
    background: linear-gradient(135deg, #333, #444);
    border-color: #555;
    transform: translateY(-2px);
}

.category-tab.active {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-color: #ff6b35;
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.tab-count {
    font-size: 12px;
    opacity: 0.8;
    margin-left: 5px;
}

/* Movie Slider Wrapper */
.movie-slider-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Slider Arrows */
.slider-arrow {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.slider-arrow:hover {
    background: linear-gradient(135deg, #e55a2b, #e8841a);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.slider-arrow:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.arrow-icon {
    font-size: 24px;
    font-weight: bold;
    color: white;
    line-height: 1;
}

/* Movie Slider */
.movie-slider {
    flex: 1;
    overflow: hidden;
    border-radius: 10px;
}

.movie-slides {
    display: flex;
    gap: 15px;
    transition: transform 0.4s ease;
    padding: 10px 0;
}

/* Movie Slide Item */
.movie-slide-item {
    flex: 0 0 200px;
    background: #1a1a1a;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.movie-slide-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

.movie-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

/* Movie Poster */
.movie-poster {
    position: relative;
    width: 100%;
    height: 280px;
    overflow: hidden;
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.movie-slide-item:hover .movie-poster img {
    transform: scale(1.05);
}

/* Movie Overlay */
.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent, rgba(0,0,0,0.7));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.movie-slide-item:hover .movie-overlay {
    opacity: 1;
}

.movie-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(255, 107, 53, 0.9);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    align-self: flex-start;
}

.star-icon {
    color: #ffd700;
}

.movie-quality {
    background: rgba(0, 0, 0, 0.8);
    color: #00ff00;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    font-weight: bold;
    align-self: flex-end;
}

/* Movie Info */
.movie-info {
    padding: 15px;
    background: #1a1a1a;
}

.movie-title {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 8px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.movie-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
}

.movie-year {
    background: #333;
    padding: 2px 8px;
    border-radius: 10px;
    color: #fff;
}

.movie-audio {
    color: #ff6b35;
    font-weight: 500;
}

/* Loading Indicator */
.slider-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 40px;
    color: #999;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #333;
    border-top: 2px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .movie-slide-item {
        flex: 0 0 150px;
    }
    
    .movie-poster {
        height: 220px;
    }
    
    .slider-arrow {
        width: 40px;
        height: 40px;
    }
    
    .arrow-icon {
        font-size: 20px;
    }
    
    .category-tab {
        padding: 10px 15px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .movie-slide-item {
        flex: 0 0 120px;
    }
    
    .movie-poster {
        height: 180px;
    }
    
    .movie-info {
        padding: 10px;
    }
    
    .movie-title {
        font-size: 12px;
    }
}
